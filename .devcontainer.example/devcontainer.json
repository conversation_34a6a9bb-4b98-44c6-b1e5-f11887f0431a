// For more information on how to use this, see https://github.com/portainer/dev-toolkit

// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/go
{
	"name": "portainer-dev-toolkit",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "portainer/dev-toolkit:2024.10",

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [8000, 8999, 9000, 9443],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "go version",

	// Use 'mounts' to mount host directories and volumes into the container.
	"mounts": [
		"source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
	],

	// Configure tool-specific properties.
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-vscode.makefile-tools",
				"ms-azuretools.vscode-docker",
				"golang.Go"
			]
		}
	},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	"remoteUser": "root"
}