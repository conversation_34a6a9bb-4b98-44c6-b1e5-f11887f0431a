package docker

import (
	"github.com/gorilla/mux"
	"github.com/portainer/agent"
	"github.com/portainer/agent/http/proxy"
	"github.com/portainer/agent/http/security"
	httperror "github.com/portainer/portainer/pkg/libhttp/error"
)

// <PERSON><PERSON> represents an HTTP API handler for proxying requests to the Docker API.
type Handler struct {
	*mux.Router
	dockerProxy          *proxy.LocalProxy
	clusterProxy         *proxy.ClusterProxy
	clusterService       agent.ClusterService
	runtimeConfiguration *agent.RuntimeConfig
	useTLS               bool
}

// NewHandler returns a new instance of Hand<PERSON>.
// It sets the associated handle functions for all the Docker related HTTP endpoints.
func <PERSON>andler(clusterService agent.ClusterService, config *agent.RuntimeConfig, notaryService *security.NotaryService, useTLS bool) *Handler {
	h := &Handler{
		Router:               mux.NewRouter(),
		dockerProxy:          proxy.NewLocalProxy(),
		clusterProxy:         proxy.NewClusterProxy(useTLS),
		clusterService:       clusterService,
		runtimeConfiguration: config,
		useTLS:               useTLS,
	}

	h.PathPrefix("/").Handler(notaryService.DigitalSignatureVerification(httperror.LoggerHandler(h.dockerOperation)))
	return h
}
