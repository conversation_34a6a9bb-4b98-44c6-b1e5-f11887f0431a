package host

import (
	"net/http"

	"github.com/gorilla/mux"

	"github.com/portainer/agent"
	"github.com/portainer/agent/http/proxy"
	"github.com/portainer/agent/http/security"
	httperror "github.com/portainer/portainer/pkg/libhttp/error"
)

// <PERSON><PERSON> represents an HTTP API Handler for host specific actions
type Handler struct {
	*mux.Router
	systemService agent.SystemService
}

// NewHandler returns a new instance of <PERSON><PERSON>
func NewHandler(systemService agent.SystemService, agentProxy *proxy.AgentProxy, notaryService *security.NotaryService) *Handler {
	h := &Handler{
		Router:        mux.NewRouter(),
		systemService: systemService,
	}

	h.<PERSON>("/host/info",
		agentProxy.Redirect(notaryService.DigitalSignatureVerification(httperror.LoggerHandler(h.hostInfo)))).Methods(http.MethodGet)

	return h
}
