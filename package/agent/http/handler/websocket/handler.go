package websocket

import (
	"github.com/portainer/agent"
	"github.com/portainer/agent/http/security"
	"github.com/portainer/agent/kubernetes"
	httperror "github.com/portainer/portainer/pkg/libhttp/error"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
)

type (
	// <PERSON><PERSON> represents an HTTP API handler for proxying requests to a web socket.
	Handler struct {
		*mux.Router
		clusterService       agent.ClusterService
		connectionUpgrader   websocket.Upgrader
		runtimeConfiguration *agent.RuntimeConfig
		kubeClient           *kubernetes.KubeClient
	}

	execStartOperationPayload struct {
		Tty    bool
		Detach bool
	}
)

// NewHandler returns a new instance of Handler.
func NewHandler(clusterService agent.ClusterService, config *agent.RuntimeConfig, notaryService *security.NotaryService, kubeClient *kubernetes.KubeClient) *Handler {
	h := &Handler{
		Router:               mux.NewRouter(),
		connectionUpgrader:   websocket.Upgrader{},
		clusterService:       clusterService,
		runtimeConfiguration: config,
		kubeClient:           kubeClient,
	}

	h.<PERSON>("/websocket/attach", notaryService.DigitalSignatureVerification(httperror.LoggerHandler(h.websocketAttach)))
	h.Handle("/websocket/exec", notaryService.DigitalSignatureVerification(httperror.LoggerHandler(h.websocketExec)))
	h.Handle("/websocket/pod", notaryService.DigitalSignatureVerification(httperror.LoggerHandler(h.websocketPodExec)))
	return h
}
