version: "2"
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - depguard
    - errorlint
    - forbidigo
    - govet
    - ineffassign
    - intrange
    - staticcheck
    - unused
    - mirror
    - durationcheck
    - errorlint
    - govet
    - unconvert
    - usetesting
    - zerologlint
  settings:
    staticcheck:
      checks: ["all", "-SA1019", "-ST1003", "-ST1005", "-ST1016", "-QF1002", "-QF1003", "-QF1008"]
    depguard:
      rules:
        main:
          files:
            - '!**/*_test.go'
          deny:
            - pkg: golang.org/x/exp
              desc: exp is not allowed
            - pkg: github.com/portainer/libcrypto
              desc: use github.com/portainer/portainer/pkg/libcrypto
            - pkg: github.com/portainer/libhttp
              desc: use github.com/portainer/portainer/pkg/libhttp
    forbidigo:
      forbid:
        - pattern: ^tls\.Config$
          msg: Use crypto.CreateTLSConfiguration() instead
        - pattern: ^tls\.Config\.(InsecureSkipVerify|MinVersion|MaxVersion|CipherSuites|CurvePreferences)$
          msg: Do not set this field directly, use crypto.CreateTLSConfiguration() instead
      analyze-types: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
