// Code generated by MockGen. DO NOT EDIT.
// Source: ./edge/client/interface.go
//
// Generated by this command:
//
//	mockgen -package mocks -source=./edge/client/interface.go -destination=./internals/mocks/mock_edge.go
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"
	time "time"

	agent "github.com/portainer/agent"
	client "github.com/portainer/agent/edge/client"
	portainer "github.com/portainer/portainer/api"
	edge "github.com/portainer/portainer/api/edge"
	gomock "go.uber.org/mock/gomock"
)

// MockPortainerClient is a mock of PortainerClient interface.
type MockPortainerClient struct {
	ctrl     *gomock.Controller
	recorder *MockPortainerClientMockRecorder
	isgomock struct{}
}

// MockPortainerClientMockRecorder is the mock recorder for MockPortainerClient.
type MockPortainerClientMockRecorder struct {
	mock *MockPortainerClient
}

// NewMockPortainerClient creates a new mock instance.
func NewMockPortainerClient(ctrl *gomock.Controller) *MockPortainerClient {
	mock := &MockPortainerClient{ctrl: ctrl}
	mock.recorder = &MockPortainerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPortainerClient) EXPECT() *MockPortainerClientMockRecorder {
	return m.recorder
}

// EnqueueLogCollectionForStack mocks base method.
func (m *MockPortainerClient) EnqueueLogCollectionForStack(logCmd client.LogCommandData) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EnqueueLogCollectionForStack", logCmd)
}

// EnqueueLogCollectionForStack indicates an expected call of EnqueueLogCollectionForStack.
func (mr *MockPortainerClientMockRecorder) EnqueueLogCollectionForStack(logCmd any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnqueueLogCollectionForStack", reflect.TypeOf((*MockPortainerClient)(nil).EnqueueLogCollectionForStack), logCmd)
}

// GetEdgeConfig mocks base method.
func (m *MockPortainerClient) GetEdgeConfig(id client.EdgeConfigID) (*client.EdgeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEdgeConfig", id)
	ret0, _ := ret[0].(*client.EdgeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEdgeConfig indicates an expected call of GetEdgeConfig.
func (mr *MockPortainerClientMockRecorder) GetEdgeConfig(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEdgeConfig", reflect.TypeOf((*MockPortainerClient)(nil).GetEdgeConfig), id)
}

// GetEdgeStackConfig mocks base method.
func (m *MockPortainerClient) GetEdgeStackConfig(edgeStackID int, version *int) (*edge.StackPayload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEdgeStackConfig", edgeStackID, version)
	ret0, _ := ret[0].(*edge.StackPayload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEdgeStackConfig indicates an expected call of GetEdgeStackConfig.
func (mr *MockPortainerClientMockRecorder) GetEdgeStackConfig(edgeStackID, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEdgeStackConfig", reflect.TypeOf((*MockPortainerClient)(nil).GetEdgeStackConfig), edgeStackID, version)
}

// GetEnvironmentID mocks base method.
func (m *MockPortainerClient) GetEnvironmentID() (portainer.EndpointID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnvironmentID")
	ret0, _ := ret[0].(portainer.EndpointID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnvironmentID indicates an expected call of GetEnvironmentID.
func (mr *MockPortainerClientMockRecorder) GetEnvironmentID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnvironmentID", reflect.TypeOf((*MockPortainerClient)(nil).GetEnvironmentID))
}

// GetEnvironmentStatus mocks base method.
func (m *MockPortainerClient) GetEnvironmentStatus(flags ...string) (*client.PollStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range flags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEnvironmentStatus", varargs...)
	ret0, _ := ret[0].(*client.PollStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnvironmentStatus indicates an expected call of GetEnvironmentStatus.
func (mr *MockPortainerClientMockRecorder) GetEnvironmentStatus(flags ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnvironmentStatus", reflect.TypeOf((*MockPortainerClient)(nil).GetEnvironmentStatus), flags...)
}

// SetEdgeConfigState mocks base method.
func (m *MockPortainerClient) SetEdgeConfigState(id client.EdgeConfigID, state client.EdgeConfigStateType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEdgeConfigState", id, state)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEdgeConfigState indicates an expected call of SetEdgeConfigState.
func (mr *MockPortainerClientMockRecorder) SetEdgeConfigState(id, state any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEdgeConfigState", reflect.TypeOf((*MockPortainerClient)(nil).SetEdgeConfigState), id, state)
}

// SetEdgeJobStatus mocks base method.
func (m *MockPortainerClient) SetEdgeJobStatus(edgeJobStatus agent.EdgeJobStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEdgeJobStatus", edgeJobStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEdgeJobStatus indicates an expected call of SetEdgeJobStatus.
func (mr *MockPortainerClientMockRecorder) SetEdgeJobStatus(edgeJobStatus any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEdgeJobStatus", reflect.TypeOf((*MockPortainerClient)(nil).SetEdgeJobStatus), edgeJobStatus)
}

// SetEdgeStackStatus mocks base method.
func (m *MockPortainerClient) SetEdgeStackStatus(edgeStackID, version int, edgeStackStatus portainer.EdgeStackStatusType, rollbackTo *int, errMessage string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEdgeStackStatus", edgeStackID, version, edgeStackStatus, rollbackTo, errMessage)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEdgeStackStatus indicates an expected call of SetEdgeStackStatus.
func (mr *MockPortainerClientMockRecorder) SetEdgeStackStatus(edgeStackID, version, edgeStackStatus, rollbackTo, errMessage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEdgeStackStatus", reflect.TypeOf((*MockPortainerClient)(nil).SetEdgeStackStatus), edgeStackID, version, edgeStackStatus, rollbackTo, errMessage)
}

// SetLastCommandTimestamp mocks base method.
func (m *MockPortainerClient) SetLastCommandTimestamp(timestamp time.Time) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLastCommandTimestamp", timestamp)
}

// SetLastCommandTimestamp indicates an expected call of SetLastCommandTimestamp.
func (mr *MockPortainerClientMockRecorder) SetLastCommandTimestamp(timestamp any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLastCommandTimestamp", reflect.TypeOf((*MockPortainerClient)(nil).SetLastCommandTimestamp), timestamp)
}

// SetTimeout mocks base method.
func (m *MockPortainerClient) SetTimeout(t time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTimeout", t)
}

// SetTimeout indicates an expected call of SetTimeout.
func (mr *MockPortainerClientMockRecorder) SetTimeout(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTimeout", reflect.TypeOf((*MockPortainerClient)(nil).SetTimeout), t)
}
