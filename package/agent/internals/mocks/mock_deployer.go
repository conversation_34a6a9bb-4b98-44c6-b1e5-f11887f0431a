// Code generated by MockGen. DO NOT EDIT.
// Source: ./deployer/interface.go
//
// Generated by this command:
//
//	mockgen -package mocks -source=./deployer/interface.go -destination=./internals/mocks/mock_deployer.go
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	agent "github.com/portainer/agent"
	deployer "github.com/portainer/agent/deployer"
	libstack "github.com/portainer/portainer/pkg/libstack"
	gomock "go.uber.org/mock/gomock"
)

// MockDeployer is a mock of Deployer interface.
type MockDeployer struct {
	ctrl     *gomock.Controller
	recorder *MockDeployerMockRecorder
	isgomock struct{}
}

// MockDeployerMockRecorder is the mock recorder for MockDeployer.
type MockDeployerMockRecorder struct {
	mock *MockDeployer
}

// NewMockDeployer creates a new mock instance.
func NewMockDeployer(ctrl *gomock.Controller) *MockDeployer {
	mock := &MockDeployer{ctrl: ctrl}
	mock.recorder = &MockDeployerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeployer) EXPECT() *MockDeployerMockRecorder {
	return m.recorder
}

// Deploy mocks base method.
func (m *MockDeployer) Deploy(ctx context.Context, name string, filePaths []string, options deployer.DeployOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deploy", ctx, name, filePaths, options)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deploy indicates an expected call of Deploy.
func (mr *MockDeployerMockRecorder) Deploy(ctx, name, filePaths, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deploy", reflect.TypeOf((*MockDeployer)(nil).Deploy), ctx, name, filePaths, options)
}

// GetEdgeStacks mocks base method.
func (m *MockDeployer) GetEdgeStacks(ctx context.Context) ([]agent.EdgeStack, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEdgeStacks", ctx)
	ret0, _ := ret[0].([]agent.EdgeStack)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEdgeStacks indicates an expected call of GetEdgeStacks.
func (mr *MockDeployerMockRecorder) GetEdgeStacks(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEdgeStacks", reflect.TypeOf((*MockDeployer)(nil).GetEdgeStacks), ctx)
}

// Pull mocks base method.
func (m *MockDeployer) Pull(ctx context.Context, name string, filePaths []string, options deployer.PullOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Pull", ctx, name, filePaths, options)
	ret0, _ := ret[0].(error)
	return ret0
}

// Pull indicates an expected call of Pull.
func (mr *MockDeployerMockRecorder) Pull(ctx, name, filePaths, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Pull", reflect.TypeOf((*MockDeployer)(nil).Pull), ctx, name, filePaths, options)
}

// Remove mocks base method.
func (m *MockDeployer) Remove(ctx context.Context, name string, filePaths []string, options deployer.RemoveOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", ctx, name, filePaths, options)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockDeployerMockRecorder) Remove(ctx, name, filePaths, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockDeployer)(nil).Remove), ctx, name, filePaths, options)
}

// Validate mocks base method.
func (m *MockDeployer) Validate(ctx context.Context, name string, filePaths []string, options deployer.ValidateOptions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", ctx, name, filePaths, options)
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockDeployerMockRecorder) Validate(ctx, name, filePaths, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockDeployer)(nil).Validate), ctx, name, filePaths, options)
}

// WaitForStatus mocks base method.
func (m *MockDeployer) WaitForStatus(ctx context.Context, name string, status libstack.Status, options deployer.CheckStatusOptions) libstack.WaitResult {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitForStatus", ctx, name, status, options)
	ret0, _ := ret[0].(libstack.WaitResult)
	return ret0
}

// WaitForStatus indicates an expected call of WaitForStatus.
func (mr *MockDeployerMockRecorder) WaitForStatus(ctx, name, status, options any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitForStatus", reflect.TypeOf((*MockDeployer)(nil).WaitForStatus), ctx, name, status, options)
}
