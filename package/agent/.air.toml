root = "."
testdata_dir = "testdata"
tmp_dir = ".tmp"

[build]
  args_bin = []
  bin = "./dist/agent"
  cmd = "go build -o ./dist/agent ./cmd/agent/main.go"
  delay = 1000
  exclude_dir = ["build", ".tmp", "dev-scripts", "static"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = "ASSETS_PATH=/root/workspace/agent/dist ./dist/agent"
  include_dir = []
  include_ext = ["go"]
  include_file = []
  kill_delay = "0s"
  log = "build-errors.log"
  poll = false
  poll_interval = 0
  post_cmd = []
  pre_cmd = []
  rerun = false
  rerun_delay = 500
  send_interrupt = false
  stop_on_error = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  silent = false
  time = false

[misc]
  clean_on_exit = false

[proxy]
  app_port = 0
  enabled = false
  proxy_port = 0

[screen]
  clear_on_rebuild = false
  keep_scroll = true
