# See: https://gist.github.com/asukakenji/f15ba7e588ac42795f421b48b8aede63
# For a list of valid GOOS and GOARCH values
# Note: these can be overriden on the command line e.g. `make PLATFORM=<platform> ARCH=<arch>`
PLATFORM=$(shell go env GOOS)
ARCH=$(shell go env GOARCH)

# build target, can be one of "production", "testing", "development"
ENV=development
TAG=local

GIT_COMMIT=$(shell git log -1 --format=%h)

# Don't change anything below this line unless you know what you're doing
.DEFAULT_GOAL := help

##@ Build dependencies
.PHONY: tidy

CE=develop
upgrade-libportainer: ## Upgrade the portainer-ce dependency ; use `make upgrade-libportainer CE="branch/ref/on/CE"` to reference any CE branch
	GOPROXY=direct go get github.com/portainer/portainer@$(CE) && go mod tidy

AGENT=develop
upgrade-libagent: ## Upgrade the agent dependency ; use `make upgrade-libagent AGENT="branch/ref/on/CE"` to reference any CE branch
	GOPROXY=direct go get github.com/portainer/agent@$(AGENT) && go mod tidy

tidy: ## Tidy up the go.mod file
	go mod tidy

##@ Hollow Agent
.PHONY: build
build: ## Build the hollow agent
	@echo "Building Hollow agent..."
	@CGO_ENABLED=0 GOOS=$(PLATFORM) GOARCH=$(ARCH) go build -trimpath --installsuffix cgo --ldflags "-s" -o dist/hollow-agent ./cmd/hollow-agent

dev: build ## Run the hollow agent
	@echo "Running Hollow agent..."
	@./dist/hollow-agent --num-agents=10 --async-mode

build-image: build ## Build the Hollow agent image locally
	docker build -f build/linux/Dockerfile -t portainerci/hollow-agent:$(TAG) --build-arg GIT_COMMIT=$(GIT_COMMIT) .

start-image: build-image ## Start the Hollow agent container
	docker rm -f hollow-agent || true
	@echo "Set a reachable Portainer address"
	@read -p "PORTAINER_ADDR(http://<ip>:<port>): " PORTAINER_ADDR; \
	docker run -d --name hollow-agent -e PORTAINER_ADDR="$$PORTAINER_ADDR" portainerci/hollow-agent:$(TAG)

##@ Cleanup
.PHONY: clean
clean: ## Remove all build and download artifacts
	@echo "Clearing the dist directory..."
	@rm -rf dist/*

lint:
	golangci-lint run --timeout=10m -c .golangci.yaml

format:
	go fmt ./...

##@ Helpers
.PHONY: help
help:  ## Display this help
	@awk 'BEGIN {FS = ":.*##"; printf "Usage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
