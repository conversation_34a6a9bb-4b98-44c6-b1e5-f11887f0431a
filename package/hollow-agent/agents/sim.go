package agents

import (
	"errors"
	"fmt"
	"iter"
	"net"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/portainer/agent"
	"github.com/portainer/agent/edge/client"
	hollowagent "github.com/portainer/hollow-agent"
	portainer "github.com/portainer/portainer/api"
	"github.com/portainer/portainer/api/edge"

	"github.com/rs/zerolog/log"
)

type stackState struct {
	version    int
	nextStatus func() (portainer.EdgeStackStatusType, bool)
	stopStatus func()
}

func CreateEdgeAgent(wg *sync.WaitGroup, i int, flags *hollowagent.Flags) {
	defer wg.Done()

	ag := &Agent{EdgeID: fmt.Sprintf("hollow-agent-%04d", i+1)}

	httpClient := client.BuildHTTPClient(30, &agent.Options{
		EdgeInsecurePoll: *flags.EdgeInsecurePoll,
		SSLCert:          *flags.MTLSCert,
		SSLKey:           *flags.<PERSON><PERSON><PERSON><PERSON><PERSON>,
		SSLCACert:        *flags.MTLSCACert,
	})

	if len(*flags.IPAddrs) > 0 {
		httpClient.SetLocalAddr(&net.TCPAddr{
			IP: (*flags.IPAddrs)[i%len(*flags.IPAddrs)],
		})
	}

	tagIDs := flags.TagIDsSlice()

	edgeClient := client.NewPortainerEdgeClient(
		*flags.PortainerAddr,
		ag.SetEndpointID,
		ag.GetEndpointID,
		ag.EdgeID,
		parseContainerPlatform(*flags.ContainerPlatform),
		agent.EdgeMetaFields{
			EdgeGroupsIDs:      []int{*flags.EdgeGroupID},
			EnvironmentGroupID: *flags.GroupID,
			TagsIDs:            tagIDs,
		},
		httpClient,
		client.WithVersion(*flags.AgentVersion),
	)

	var endpointID portainer.EndpointID

	var stackVersions map[int]*stackState
	var edgeStackConfigMap map[int]*edge.StackPayload
	sleepDuration := time.Duration(*flags.PollingInterval) * time.Second
	for ; ; time.Sleep(sleepDuration) {
		if ag.GetEndpointID() == 0 {
			var err error
			stackVersions = make(map[int]*stackState)
			edgeStackConfigMap = make(map[int]*edge.StackPayload)

			if endpointID, err = edgeClient.GetEnvironmentID(); err != nil || endpointID == 0 {
				log.Error().Err(err).Msg("failed to get environment ID")

				continue
			}

			log.Info().Int("EndpointID", int(endpointID)).Msg("Setting endpointID")
			ag.SetEndpointID(endpointID)
		}

		resp, err := edgeClient.GetEnvironmentStatus()
		if err != nil {
			log.Debug().Err(err).Msg("failed to get environment status")

			continue
		}

		for edgeConfigID, state := range resp.EdgeConfigurations {
			if state == client.EdgeConfigIdleState {
				continue
			}

			if _, err := edgeClient.GetEdgeConfig(edgeConfigID); err != nil {
				log.Debug().Err(err).Msg("failed to get edge config")

				continue
			}

			log.Debug().
				Str("EdgeID", ag.EdgeID).
				Int("EndpointID", int(ag.GetEndpointID())).
				Str("Context", "UpdateEdgeAgentStatus").
				Int("EdgeConfigID", int(edgeConfigID)).
				Str("State", state.String()).
				Msg("Setting the state for edge config")

			if err := edgeClient.SetEdgeConfigState(edgeConfigID, client.EdgeConfigIdleState); err != nil {
				log.Debug().Err(err).Msg("failed to set edge config state")

				continue
			}
		}

		for _, stack := range resp.Stacks {
			currentState, ok := stackVersions[stack.ID]
			if !ok || currentState.version < stack.Version {
				next, stop := iter.Pull(slices.Values(statusSequence))

				if currentState != nil && currentState.stopStatus != nil {
					currentState.stopStatus()
				}
				currentState = &stackState{version: stack.Version, nextStatus: next, stopStatus: stop}
				stackVersions[stack.ID] = currentState
			}

			nextStatus, ok := currentState.nextStatus()
			if !ok {
				continue
			}

			if nextStatus == portainer.EdgeStackStatusAcknowledged {
				edgeStackConfig, err := edgeClient.GetEdgeStackConfig(stack.ID, &stack.Version)
				if err != nil {
					var nonOkError *client.NonOkResponseError
					if errors.As(err, &nonOkError) {
						ag.SetEndpointID(0)
					}

					log.Debug().Err(err).Msg("failed to get edge stack config")

					delete(stackVersions, stack.ID)

					continue
				}

				// if the edge stack is a "remote update", the sequence of statuses is different and needs to be updated
				if edgeStackConfig.EdgeUpdateID > 0 {
					next, stop := iter.Pull(slices.Values(remoteUpdateStatusSequence))

					if currentState != nil && currentState.stopStatus != nil {
						currentState.stopStatus()
					}
					currentState = &stackState{version: stack.Version, nextStatus: next, stopStatus: stop}
					nextStatus, _ = currentState.nextStatus()
					stackVersions[stack.ID] = currentState
				}

				edgeStackConfigMap[stack.ID] = edgeStackConfig
			}

			log.Debug().
				Str("EdgeID", ag.EdgeID).
				Int("EndpointID", int(ag.GetEndpointID())).
				Int("Status", int(nextStatus)).
				Int("Version", stack.Version).
				Str("Context", "UpdateEdgeAgentStatus").
				Msg("Setting the status for agent")

			if err := edgeClient.SetEdgeStackStatus(stack.ID, stack.Version, nextStatus, nil, ""); err != nil {
				log.Debug().Err(err).Msg("failed to set edge stack status")

				continue
			}

			currentState.version = stack.Version

			edgeConfig, ok := edgeStackConfigMap[stack.ID]
			if !ok {
				log.Error().Msg("failed to get edge config, cannot update edge client")

				continue
			}

			// If the stack is an edge update, in EdgeStackStatusDeploying state, we need to simulate the handover
			// from the old agent to the new agent. This is done by updating the edge client with the new edge client properties
			if edgeConfig.EdgeUpdateID > 0 && nextStatus == portainer.EdgeStackStatusDeploying {
				log.Debug().
					Str("EdgeID", ag.EdgeID).
					Int("EndpointID", int(ag.GetEndpointID())).
					Str("Context", "UpdateEdgeAgentStatus").
					Int("EdgeConfigID", edgeConfig.ID).
					Int("EdgeUpdateID", edgeConfig.EdgeUpdateID).
					Msg("updating edge client with new edge client properties to complete remote update")

				newAgentVersion, err := getNewAgentVersion(edgeConfig.EntryFileName, edgeConfig.DirEntries)
				if err != nil {
					log.Error().Err(err).Msg("failed to get new agent version")

					continue
				}

				edgeClient = client.NewPortainerEdgeClient(
					*flags.PortainerAddr,
					ag.SetEndpointID,
					ag.GetEndpointID,
					ag.EdgeID,
					parseContainerPlatform(*flags.ContainerPlatform),
					agent.EdgeMetaFields{
						EdgeGroupsIDs:      []int{*flags.EdgeGroupID},
						EnvironmentGroupID: *flags.GroupID,
						UpdateID:           edgeConfig.EdgeUpdateID, // UpdateID is used to identify the edge client as part of the remote update
						TagsIDs:            tagIDs,
					},
					httpClient,
					client.WithVersion(newAgentVersion),
				)

				delete(edgeStackConfigMap, stack.ID)
			}
		}

		for _, schedule := range resp.Schedules {
			// if logs are requested, immediately store a mock log to be sent on the next poll
			if schedule.CollectLogs {
				if err := edgeClient.SetEdgeJobStatus(agent.EdgeJobStatus{
					JobID:          schedule.ID,
					LogFileContent: strconv.Itoa(int(ag.GetEndpointID())) + "\n" + strings.Repeat("log line\n", 10),
				}); err != nil {
					log.Error().Err(err).Msg("failed to set edge job status")
				}

				log.Debug().
					Str("EdgeID", ag.EdgeID).
					Int("EndpointID", int(ag.GetEndpointID())).
					Str("Context", "UpdateEdgeJob").
					Int("EdgeJobID", schedule.ID).
					Msg("Setting the state for edge job to return logs")
			}
		}
	}
}
