package agents

import (
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/portainer/agent"
	"github.com/portainer/agent/docker"
	"github.com/portainer/agent/edge/client"
	hollowagent "github.com/portainer/hollow-agent"
	portainer "github.com/portainer/portainer/api"
	"github.com/portainer/portainer/api/edge"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
	"github.com/mitchellh/mapstructure"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/singleflight"
)

const stackOpDelay = 5 * time.Second

func handleNonOkError(err error, ag *Agent, stackVersions map[int]int) {
	var nonOkError *client.NonOkResponseError
	if errors.As(err, &nonOkError) {
		ag.SetEndpointID(0)
		clear(stackVersions)
	}
}

func CreateEdgeAsyncAgent(wg *sync.WaitGroup, i int, flags *hollowagent.Flags) {
	defer wg.Done()

	ag := &Agent{EdgeID: fmt.Sprintf("hollow-async-agent-%04d", i+1)}

	httpClient := client.BuildHTTPClient(30, &agent.Options{
		EdgeInsecurePoll: *flags.EdgeInsecurePoll,
		SSLCert:          *flags.MTLSCert,
		SSLKey:           *flags.MTLSKey,
		SSLCACert:        *flags.MTLSCACert,
	})

	if len(*flags.IPAddrs) > 0 {
		httpClient.SetLocalAddr(&net.TCPAddr{
			IP: (*flags.IPAddrs)[i%len(*flags.IPAddrs)],
		})
	}

	tagIDs := flags.TagIDsSlice()

	hollowSnapshotter := &snapshotter{
		EmptySnapshot: *flags.EmptySnapshot,
	}
	edgeClient := client.NewPortainerAsyncClient(
		*flags.PortainerAddr,
		ag.SetEndpointID,
		ag.GetEndpointID,
		ag.EdgeID,
		"",
		parseContainerPlatform(*flags.ContainerPlatform),
		agent.EdgeMetaFields{
			EdgeGroupsIDs:      []int{*flags.EdgeGroupID},
			EnvironmentGroupID: *flags.GroupID,
			TagsIDs:            tagIDs,
		},
		httpClient,
		client.WithVersion(*flags.AgentVersion),
		client.WithDockerSnapshotter(hollowSnapshotter),
	)

	stackVersions := make(map[int]int)

	sleepDuration := time.Duration(*flags.PollingInterval) * time.Second

mainLoop:
	for ; ; time.Sleep(sleepDuration) {
		resp, err := edgeClient.GetEnvironmentStatus("command", "snapshot")
		if err != nil {
			log.Debug().Err(err).
				Str("EdgeID", ag.EdgeID).
				Str("Flags", "command,snapshot").
				Str("Context", "GetEdgeAsyncEnvironmentStatusInMainLoop").
				Msg("failed to get environment status")

			handleNonOkError(err, ag, stackVersions)

			continue
		}

		for _, command := range resp.AsyncCommands {
			switch command.Type {
			case "edgeStack":
				var stackData edge.StackPayload
				if err := mapstructure.Decode(command.Value, &stackData); err != nil {
					log.Error().Err(err).Msg("failed to decode stack data")

					continue
				}

				switch command.Operation {
				case "add", "replace":
					if currentVer, ok := stackVersions[stackData.ID]; ok && currentVer == stackData.Version {
						continue
					}

					statuses := statusSequence
					// if the edge stack is a "remote update", the sequence of statuses is different and needs to be updated
					if stackData.EdgeUpdateID > 0 {
						statuses = remoteUpdateStatusSequence
					}

					for _, status := range statuses {
						log.Debug().Str("EdgeID", ag.EdgeID).
							Int("EndpointID", int(ag.GetEndpointID())).
							Int("Status", int(status)).
							Int("Version", stackData.Version).
							Str("Operation", command.Operation).
							Str("Context", "UpdateEdgeAsyncAgentStatus").
							Msg("Setting the status for async agent")

						if err := edgeClient.SetEdgeStackStatus(stackData.ID, stackData.Version, status, nil, ""); err != nil {
							log.Debug().Err(err).Msg("failed to set edge stack status")

							break mainLoop
						}

						// If the stack is an edge update, in EdgeStackStatusDeploying state, we need to simulate the handover
						// from the old agent to the new agent. This is done by updating the edge client with the new edge client properties
						if stackData.EdgeUpdateID > 0 && status == portainer.EdgeStackStatusDeploying {
							log.Debug().
								Str("EdgeID", ag.EdgeID).
								Int("EndpointID", int(ag.GetEndpointID())).
								Str("Context", "UpdateEdgeAgentStatus").
								Int("EdgeStackID", stackData.ID).
								Int("EdgeUpdateID", stackData.EdgeUpdateID).
								Msg("updating edge client with new edge client properties to complete remote update")

							newAgentVersion, err := getNewAgentVersion(stackData.EntryFileName, stackData.DirEntries)
							if err != nil {
								log.Error().Err(err).Msg("failed to get new agent version")
								continue
							}

							edgeClient = client.NewPortainerAsyncClient(
								*flags.PortainerAddr,
								ag.SetEndpointID,
								ag.GetEndpointID,
								ag.EdgeID,
								"",
								parseContainerPlatform(*flags.ContainerPlatform),
								agent.EdgeMetaFields{
									EdgeGroupsIDs:      []int{*flags.EdgeGroupID},
									UpdateID:           stackData.EdgeUpdateID,
									EnvironmentGroupID: *flags.GroupID,
									TagsIDs:            tagIDs,
								},
								httpClient,
								client.WithVersion(newAgentVersion),
								client.WithDockerSnapshotter(hollowSnapshotter),
							)
						}

						time.Sleep(stackOpDelay)
					}

					stackVersions[stackData.ID] = stackData.Version

				case "remove":
					for _, status := range removeStatusSequence {
						log.Debug().Str("EdgeID", ag.EdgeID).
							Int("EndpointID", int(ag.GetEndpointID())).
							Int("Status", int(status)).
							Int("Version", stackData.Version).
							Str("Operation", command.Operation).
							Str("Context", "UpdateEdgeAsyncAgentStatus").
							Msg("Setting the status for async agent")

						if err := edgeClient.SetEdgeStackStatus(stackData.ID, stackData.Version, status, nil, ""); err != nil {
							log.Debug().Err(err).Msg("failed to set edge stack status")

							break mainLoop
						}

						time.Sleep(stackOpDelay)
					}

					delete(stackVersions, stackData.ID)
				}
			case "edgeConfig":

				var configData client.EdgeConfig
				if err := mapstructure.Decode(command.Value, &configData); err != nil {
					log.Error().Err(err).Msg("failed to decode edge config data")

					continue
				}

				state := client.EdgeConfigIdleState

				if configData.Invalid {
					state = client.EdgeConfigFailureState
				}

				log.Debug().
					Str("EdgeID", ag.EdgeID).
					Int("EndpointID", int(ag.GetEndpointID())).
					Str("Context", "UpdateEdgeAgentStatus").
					Int("EdgeConfigID", int(configData.ID)).
					Str("State", state.String()).
					Msg("Setting the state for edge config")

				if err := edgeClient.SetEdgeConfigState(configData.ID, state); err != nil {
					log.Debug().Err(err).Msg("failed to set edge config state")

					continue
				}

			case "edgeJob":
				var jobData client.EdgeJobData
				if err := mapstructure.Decode(command.Value, &jobData); err != nil {
					log.Error().Err(err).Msg("failed to decode edge job data")

					continue
				}

				// if logs are requested, immediately store a mock log to be sent on the next poll
				if jobData.CollectLogs {
					if err := edgeClient.SetEdgeJobStatus(agent.EdgeJobStatus{
						JobID:          int(jobData.ID),
						LogFileContent: strconv.Itoa(int(ag.GetEndpointID())) + "\n" + strings.Repeat("log line\n", 10),
					}); err != nil {
						log.Error().Err(err).Msg("failed to set edge job status")
					}

					log.Debug().
						Str("EdgeID", ag.EdgeID).
						Int("EndpointID", int(ag.GetEndpointID())).
						Str("Context", "UpdateEdgeJob").
						Int("EdgeJobID", int(jobData.ID)).
						Msg("Setting the state for edge job to return logs")
				}

			default:
				log.Info().Str("EdgeID", ag.EdgeID).
					Int("EndpointID", int(ag.GetEndpointID())).
					Str("Context", "UpdateEdgeAgentStatus").
					Str("Command", command.Type).
					Msg("Unknown command, skipping")
			}

			edgeClient.SetLastCommandTimestamp(command.Timestamp)
		}
	}
}

type snapshotter struct {
	EmptySnapshot bool
}

var snapshotSingleflight singleflight.Group

func (s *snapshotter) CreateSnapshot(edgeKey string) (*portainer.DockerSnapshot, error) {
	snapshot, err, _ := snapshotSingleflight.Do(edgeKey, func() (any, error) {
		var snapshotErr error
		for range 10 {
			time.Sleep(1 * time.Second)
			dockerSnapshot, err := docker.Snapshotter{}.CreateSnapshot(edgeKey)
			if err == nil {
				return dockerSnapshot, nil
			}
			snapshotErr = err
		}

		return nil, snapshotErr
	})
	if err != nil {
		return nil, err
	}

	dockerSnapshot := snapshot.(*portainer.DockerSnapshot)

	dockerSnapshotCopy := *dockerSnapshot

	dockerSnapshotCopy.SnapshotRaw.Containers = []portainer.DockerContainerSnapshot{}
	dockerSnapshotCopy.SnapshotRaw.Volumes = volume.ListResponse{}
	dockerSnapshotCopy.SnapshotRaw.Networks = []network.Summary{}
	dockerSnapshotCopy.SnapshotRaw.Images = []image.Summary{}

	if !s.EmptySnapshot {
		dockerSnapshotCopy.SnapshotRaw.Containers = deepCopyContainers(dockerSnapshot.SnapshotRaw.Containers)
		dockerSnapshotCopy.SnapshotRaw.Volumes = deepCopyVolumes(dockerSnapshot.SnapshotRaw.Volumes)
		dockerSnapshotCopy.SnapshotRaw.Networks = deepCopyNetworks(dockerSnapshot.SnapshotRaw.Networks)
		dockerSnapshotCopy.SnapshotRaw.Images = deepCopyImages(dockerSnapshot.SnapshotRaw.Images)
	}

	return &dockerSnapshotCopy, err
}

func deepCopyContainers(containers []portainer.DockerContainerSnapshot) []portainer.DockerContainerSnapshot {
	cCopy := make([]portainer.DockerContainerSnapshot, len(containers))
	copy(cCopy, containers)
	for i, c := range containers {
		if c.Mounts != nil {
			cCopy[i].Mounts = make([]container.MountPoint, len(c.Mounts))
			copy(cCopy[i].Mounts, c.Mounts)
		}
	}

	return cCopy
}

func deepCopyNetworks(networks []network.Summary) []network.Summary {
	nCopy := make([]network.Summary, len(networks))
	copy(nCopy, networks)

	return nCopy
}

func deepCopyImages(images []image.Summary) []image.Summary {
	iCopy := make([]image.Summary, len(images))
	copy(iCopy, images)

	return iCopy
}

func deepCopyVolumes(volumes volume.ListResponse) volume.ListResponse {
	lCopy := volume.ListResponse{
		Volumes:  make([]*volume.Volume, len(volumes.Volumes)),
		Warnings: volumes.Warnings,
	}
	copy(lCopy.Volumes, volumes.Volumes)

	return lCopy
}
