package agents

import (
	"errors"
	"os"
	"regexp"

	"github.com/portainer/agent"
	portainer "github.com/portainer/portainer/api"
	"github.com/portainer/portainer/api/filesystem"
)

var statusSequence = []portainer.EdgeStackStatusType{
	portainer.EdgeStackStatusAcknowledged,
	portainer.EdgeStackStatusDeploymentReceived,
	portainer.EdgeStackStatusDeploying,
	portainer.EdgeStackStatusRunning,
}

var remoteUpdateStatusSequence = []portainer.EdgeStackStatusType{
	portainer.EdgeStackStatusAcknowledged,
	portainer.EdgeStackStatusDeploymentReceived,
	portainer.EdgeStackStatusDeploying,
}

var removeStatusSequence = []portainer.EdgeStackStatusType{
	portainer.EdgeStackStatusRemoving,
	portainer.EdgeStackStatusRemoved,
}

func parseContainerPlatform(cp string) agent.ContainerPlatform {
	switch cp {
	case "docker":
		return agent.PlatformDocker
	case "kubernetes", "k8s":
		os.Setenv("KUBERNETES_SERVICE_HOST", "1")
		os.Setenv("KUBERNETES_SERVICE_PORT", "1")

		return agent.PlatformKubernetes
	case "podman":
		return agent.PlatformPodman
	default:
		return agent.PlatformDocker
	}
}

// newVersionRegex matches the new agent version from the stack payload
var newVersionRegex = regexp.MustCompile(`"portainer(?:ci)?/agent:(.+)"`)

// getNewAgentVersion returns the new agent version from the edge config
func getNewAgentVersion(entryFileName string, dirEntries []filesystem.DirEntry) (string, error) {
	if err := filesystem.DecodeDirEntries(dirEntries); err != nil {
		return "", err
	}

	var fileContent string
	for _, dirEntry := range dirEntries {
		if dirEntry.IsFile && dirEntry.Name == entryFileName {
			fileContent = dirEntry.Content

			break
		}
	}

	if fileContent == "" {
		return "", errors.New("entryFileName not found in DirEntries")
	}

	match := newVersionRegex.FindStringSubmatch(fileContent)
	if len(match) != 2 {
		return "", errors.New("failed to find new agent version in file content")
	}

	return match[1], nil
}
