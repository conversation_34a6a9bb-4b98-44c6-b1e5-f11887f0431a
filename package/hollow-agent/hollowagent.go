package hollowagent

import (
	"net"
	"strconv"
	"strings"
)

const (
	EnvVarLogLevel          = "LOG_LEVEL"
	EnvVarLogMode           = "LOG_MODE"
	EnvVarNumAgents         = "NUM_AGENTS"
	EnvVarPortainerAddr     = "PORTAINER_ADDR"
	EnvVarEdgeGroupID       = "EDGE_GROUP_ID"
	EnvVarPollingInterval   = "POLLING_INTERVAL"
	EnvVarAsyncMode         = "ASYNC_MODE"
	EnvVarContainerPlatform = "CONTAINER_PLATFORM"
	EnvVarIPAddrs           = "IP_ADDRS"
	EnvKeyEdgeInsecurePoll  = "EDGE_INSECUREPOLL"
	EnvVarMTLSCert          = "MTLS_SSL_CERT"
	EnvVarMTLSKey           = "MTLS_SSL_KEY"
	EnvVarMTLSCACert        = "MTLS_SSL_CA"
	EnvVarAgentVersion      = "HOLLOW_AGENT_VERSION"
	EnvVarEndpointGroup     = "PORTAINER_GROUP"
	EnvVarTags              = "PORTAINER_TAGS"
	EnvVarStartupDelay      = "STARTUP_DELAY"
	EnvVarEmptySnapshot     = "EMPTY_SNAPSHOT"
)

type Flags struct {
	LogLevel          *string
	LogMode           *string
	NumAgents         *int
	PortainerAddr     *string
	EdgeGroupID       *int
	PollingInterval   *int
	AsyncMode         *bool
	ContainerPlatform *string
	IPAddrs           *[]net.IP
	EdgeInsecurePoll  *bool
	MTLSCert          *string
	MTLSKey           *string
	MTLSCACert        *string
	AgentVersion      *string
	GroupID           *int
	TagIDs            *string
	StartupDelay      *int
	EmptySnapshot     *bool
}

func (f Flags) TagIDsSlice() []int {
	if f.TagIDs == nil || *f.TagIDs == "" {
		return nil
	}

	tagIDs := make([]int, 0)
	for _, tagID := range strings.Split(*f.TagIDs, ":") {
		id, err := strconv.Atoi(tagID)
		if err != nil {
			continue
		}
		tagIDs = append(tagIDs, id)
	}

	return tagIDs
}
