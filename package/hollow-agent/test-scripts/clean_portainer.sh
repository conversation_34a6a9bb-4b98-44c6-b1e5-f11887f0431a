#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'
dir=$(dirname -- "${BASH_SOURCE[0]}")

source $dir/utils/options.sh

declare -A options
declare -A http_opt=([short]="p" [long]="http" [arg]=true [default]=9000 [help]="The http port of the Portainer instance")
declare -A runc_opt=([short]="r" [long]="runc-platform" [arg]=true [default]="docker" [help]="The runc platform to use. (docker|podman)")
register_options options http_opt runc_opt

declare -A args
declare -a pos_args
parse_args options args pos_args $@

http=${args[http]}
runc=${args["runc-platform"]}

runc=$([ "$runc" = "podman" ] && echo "sudo podman" || echo "$runc")

echo " >> Cleaning portainer container"
${runc} rm -f portainer_"$http"

echo " >> Cleaning portainer volume"
${runc} volume rm portainer_"$http"_data

echo " >> Done"
