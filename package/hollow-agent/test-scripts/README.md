# test-scripts

This folder contains scripts to quickly test and deploy the hollow agent based on configurations.

The main scripts are located at the root of the folder.

`utils` and `steps` folders contain scripts that can either be used directly, or sourced to be used as functions in other scripts.

All default values are either specified in the script args, or in `utils/default.sh`

## How to use the scripts

- `create_portainer.sh`: creates a portainer instance
  - see `--help` for all available options
- `init_portainer.sh`: inits the instance (creates admin account / validate license / enable edge compute features)
  - ⚠️ this script doesn't have an help and isn't configurable atm. it uses values of `utils/default.sh`
- `deploy_agents.sh`: deploys HA agents based on the provided config
  - see `--help` for all available options
- `clean_portainer.sh`: cleans the portainer instance
  - see `--help` for all available options
- `clean_all_hollow_agents.sh`: one-liner to remove all HA containers created by the `deploy_agents.sh` script

## More about steps/utils

(nearly) each script of both folders can be use "as-is"/called directly to reduce the burden of manually navigating the UI.

They should all contain a `--help` that will display the available options

## Missing pieces

The `deploy_agents.sh` script doesn't support the following features yet

- assign edge agents to edge groups
- deploy edge stacks
