#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'
dir=$(dirname -- "${BASH_SOURCE[0]}")

source $dir/utils/utils.sh
source $dir/utils/options.sh

declare -A options
declare -A http_opt=([long]="http" [arg]=true [default]=9000 [help]="The http port of the Portainer instance")
declare -A https_opt=([long]="https" [arg]=true [default]=9443 [help]="The https port of the Portainer instance")
declare -A tunnel_opt=([short]="t" [long]="tunnel" [arg]=true [default]=8000 [help]="The tunnel (edge) port of the Portainer instance")
declare -A runc_opt=([short]="r" [long]="runc-platform" [arg]=true [default]="docker" [help]="The runc platform to use. (docker|podman)")
declare -A image_opt=([short]="i" [long]="image" [arg]=true [default]="portainerci/portainer-ee:develop" [help]="The portainer image to deploy")
declare -A debug_opt=([short]="d" [long]="debug" [help]="Enable debug mode on the Portainer instance")
register_options options http_opt https_opt tunnel_opt runc_opt image_opt debug_opt
remove_options options dry-run url username password

declare -A args
declare -a pos_args
parse_args options args pos_args $@

http=${args[http]}
https=${args[https]}
tunnel=${args[tunnel]}
runc=${args["runc-platform"]}
image=${args[image]}
debug=${args[debug]}

socket=$([ "$runc" = "docker" ] && echo "/var/run/docker.sock" || echo "/run/podman/podman.sock")
runc=$([ "$runc" = "podman" ] && echo "sudo podman" || echo "$runc")
debug_cmd=$([ "$debug" ] && echo "--log-level=DEBUG")

echo " >> Pulling $image"
$runc pull "$image"

echo " >> Starting container"
$runc run -d \
  -p $http:9000 \
  -p $https:9443 \
  -p $tunnel:8000 \
  --name portainer_"$http" \
  -v ${socket}:${socket} \
  -v portainer_"$http"_data:/data \
  "$image" $debug_cmd

if [[ $(echo $?) -eq 0 ]]; then
  echo " >> Container running at http://localhost:$http | https://localhost:$https"
fi
