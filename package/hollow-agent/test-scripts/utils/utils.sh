#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_UTILS:-}" ]] && return
__SOURCED_SCRIPT_UTILS=true

function print_error() {
  echo >&2 -e "${1:-}"
}

function die() {
  local msg=${1:-""}
  local code=${2:-1} # default exit status 1
  if [[ -n "$msg" ]]; then
    print_error "$msg"
  fi
  exit "$code"
}

# prints the array passed as ref
#
# declare -A my_array=([key]="value")
# arr_print my_array
# # /!\ not
# arr_print $my_array
function arr_print() {
  local __p_ref="$1"
  local -n __p=$__p_ref

  local __p_numeric_keys=()
  local __p_non_numeric_keys=()

  # Separate numeric and non-numeric keys
  for key in "${!__p[@]}"; do
    if [[ "$key" =~ ^[0-9]+$ ]]; then
      __p_numeric_keys+=("$key")
    else
      __p_non_numeric_keys+=("$key")
    fi
  done

  # Sort the numeric and non-numeric keys separately
  local __p_sorted_numeric_keys=($(echo "${__p_numeric_keys[@]}" | tr ' ' '\n' | sort -n))
  local __p_sorted_non_numeric_keys=($(echo "${__p_non_numeric_keys[@]}" | tr ' ' '\n' | sort))

  # Combine the sorted keys
  local __p_sorted_keys=("${__p_sorted_numeric_keys[@]}" "${__p_sorted_non_numeric_keys[@]}")
  echo -e " --- $__p_ref ---"
  for key in "${__p_sorted_keys[@]}"; do
    echo "[$key] = ${__p[$key]}"
  done
  echo -e " --- ! $__p_ref ---"
}

# Unpack the associative array "$1" into variables "${@:2}"
#
# Example
#
# local -A values=([a]="apple" [b]="banana" [c]="cherry")
#
# # create X variables named after each item of `keys`
# # same as writing `local a b c`
# local -a keys=("a" "b" "c")
# local "${keys[@]}"
#
# # unpack the array of values into vars
# arr_unpack_associative values "${keys[@]}"
#
# # will result in
# $a="apple"
# $b="banana"
# $c="cherry"
function arr_unpack_associative() {
  local __arr_ref="$1"
  local -n __arr=$__arr_ref
  shift

  local var
  for var in "$@"; do
    local value=${__arr[$var]:-""}
    printf -v $var %s "$value"
    shift
  done
}

# unpack the indexed array "$1" into variables "${@:2}"
#
# Example
#
# local -a values=("apple" "banana" "cherry")
#
# # create X variables named after each item of `vars`
# # same as writing `local a b c`
# local -a vars=("a" "b" "c")
# local "${vars[@]}"
#
# # unpack the array of values into vars
# arr_unpack values "${vars[@]}"
#
# # will result in
# $a="apple"
# $b="banana"
# $c="cherry"
function arr_unpack_indexed() {
  local __arr_ref="$1"
  local -n __arr=$__arr_ref
  shift

  local var
  for var in ${__arr[@]}; do
    if [[ $# -eq 0 ]]; then
      return
    fi
    printf -v $1 %s "$var"
    shift
  done
}

# JSONify an array
# - keep numeric items of array raw
# - otherwise surround item with quotes
function arr_to_json() {
  local -n __arr="$1"

  if [[ ${#__arr[@]} -eq 0 ]]; then
    echo "[]"
    return
  fi

  IFS='\n' echo "${__arr[*]}" | awk '{if ($0 ~ /^-?[0-9]+$/) print $0; else print "\"" $0 "\""}' | jq -s .
}

# check if INDEXED array contains the value
# - if found return 0
# - else return 1
function arr_contains() {
  local -n __arr="$1"
  local search="$2"

  for elem in "${__arr[@]}"; do
    if [[ "$elem" == "$search" ]]; then
      return 0
    fi
  done
  return 1
}

# generate a random name using left/right lists + a random string
function random_name() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")
  local chars=({a..z} {A..Z} {0..9})
  local left=$(shuf -n 1 $dir/left.txt)
  local right=$(shuf -n 1 $dir/right.txt)
  local random_str=$(for i in {1..6}; do echo -n "${chars[RANDOM % ${#chars[@]}]}"; done)
  echo "${left}_${right}_${random_str}"
}

function run_or_print() {
  local $dry_run=$1

  if [[ $dry_run = true ]]; then
    echo "$@"
  else
    "$@"
  fi
}
