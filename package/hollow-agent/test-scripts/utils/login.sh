#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_LOGIN:-}" ]] && return
__SOURCED_SCRIPT_LOGIN=true

function login() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  source $dir/defaults.sh
  source $dir/options.sh
  source $dir/utils.sh

  local -A options
  register_options options

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local url="${args[url]}/api/auth"

  local payload=$(
    jq -n \
      --arg username "${args[username]}" \
      --arg password "${args[password]}" \
      '{ username: $username, password: $password }'
  )

  echo $(curl -s --json "$payload" "$url" | jq -r ".jwt")
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  login $@
fi
