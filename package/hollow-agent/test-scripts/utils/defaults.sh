#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_DEFAULT:-}" ]] && return
__SOURCED_SCRIPT_DEFAULT=true

DEFAULT_USERNAME="admin"
DEFAULT_PASSWORD="portainer1234"
DEFAULT_URL="http://127.0.0.1:9000"
DEFAULT_URL_HTTPS="https://127.0.0.1:9443"
DEFAULT_KEY="3-cw+v9DakBkBckG801qC4T1YMYTye5EWKr/d+kLRQc1te3NML+PUEZwujExyduRVJ5sxqIEBmoIZh/ut6d1ozEjJD"
