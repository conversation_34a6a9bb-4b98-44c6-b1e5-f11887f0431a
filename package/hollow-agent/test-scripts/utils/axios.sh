#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

## HOW TO: Generate JSON payload with jq
## -----
#
# payload=$(jq -n \
#     --arg username "example_user" \
#     --arg password "example_password" \
#     --arg<PERSON><PERSON> age 30 \
#     --arg<PERSON>son active true \
#     '{
#       username: $username,
#       password: $password,
#       attributes: {
#         age: $age,
#         active: $active
#       }
#     }')
#
## -----
##
## use
## --arg for strings
## --argjson for number | bool

[[ -n "${__SOURCED_SCRIPT_AXIOS:-}" ]] && return
__SOURCED_SCRIPT_AXIOS=true

# accepts the options of `login`
# see `login.sh` for the full list
function init_axios() {
  # return if we are already authenticated
  [[ -n "${__jwt:-}" ]] && return

  local dir=$(dirname -- "${BASH_SOURCE[0]}")
  source $dir/login.sh
  source $dir/utils.sh

  __jwt=$(login $@)
  if [[ -z "$__jwt" || "$__jwt" == "null" ]]; then
    die "Error: unable to login to the portainer instance"
  fi
}

# authenticated post to the portainer server
function post() {
  local url=$1
  local payload=$2

  if [[ -z "$__jwt" || "$__jwt" == "null" ]]; then
    die "Error: login once with 'init_axios' before attempting to use 'post'"
  fi

  if [[ $# -ne 2 ]]; then
    die """
    post function requires 2 arguments: url payload
    Example: post \"localhost:9000/api/example\" \"\$payload\"
    """
  fi

  curl -sS -H "Authorization: Bearer $__jwt" --json "$payload" "$url"
}

# authenticated put to the portainer server
function put() {
  local url=$1
  local payload=$2

  if [[ -z "$__jwt" || "$__jwt" == "null" ]]; then
    die "Error: login once with 'init_axios' before attempting to use 'put'"
  fi

  if [[ $# -ne 2 ]]; then
    die """
    put function requires 2 arguments: url payload
    Example: put \"localhost:9000/api/example\" \"\$payload\"
    """
  fi

  curl -sS -X PUT -H "Authorization: Bearer $__jwt" -d "$payload" "$url"
}

# authenticated get on the portainer server
function get() {
  local url=$1

  if [[ -z "$__jwt" || "$__jwt" == "null" ]]; then
    die "Error: login once with 'init_axios' before attempting to use 'get'"
  fi

  if [[ $# -ne 1 ]]; then
    die """
    get function requires 1 argument: url
    Example: get \"localhost:9000/api/example\"
    """
  fi

  curl -sS -H "Authorization: Bearer $__jwt" "$url"
}
