#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_OPTIONS:-}" ]] && return
__SOURCED_SCRIPT_OPTIONS=true

__parse_script_dir=$(dirname -- "${BASH_SOURCE[0]}")
source $__parse_script_dir/utils.sh

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  die "This script should only be sourced"
fi

# /!\ ONLY SOURCE THIS SCRIPT /!\
#
# Usage
#
# ```
# source ./utils/options.sh
#
# declare -A options
# declare -A optA=([short]="a" [long]="alpha")
# declare -A optB=([short]="b" [long]="beta" [arg]=true)
# declare -A optBee=([short]="c" [long]="gamma" [arg]=true [default]="bee")
# register_options options optA optB optBee
#
# declare -A args
# declare -a pos_args
# parse_args options args pos_args $@
# ```
#
# /!\ USE NAME REFS FOR register_options AND parse_args /!\
#
# # Running the script with `./myscript.sh --beta x y z` (or `./myscript.sh -b x y z` ) will result in
# # $args containing
# [alpha] = false
# [beta] = x
# [gamma] = bee
#
# # and $pos_args containing
# [0] = y
# [1] = z
#
# # Retrieve the value passed as argument to the script using ${args[OPTION]} or ${args["OPTION"]}
# # where OPTION / "OPTION" is the long form of the option
# # the value will either be
# # - the provided argument value for the option, or true if the option doesn't accepts an arg, if the option was provided
# # - or the default value if the option was not provided
# # - or false if the option was not provided and didn't have a default value (false is the default value of the option's default value)
# #
# # All the following retrieval are equivalent
#
# alpha=${args[alpha]}
# alpha=${args["alpha"]}
#
# # All the positional arguments will be saved in the `pos_args` array, in the order they were passed to the script

__internal_key="__internal"
declare -A __internal_keys=(
  [short_opts]="${__internal_key}_opts_short"
  [long_opts]="${__internal_key}_opts_long"
  [required]="${__internal_key}_required"
  [seen]="${__internal_key}_seen"
  [arg]="${__internal_key}_arg"
  [default]="${__internal_key}_default"
  [help]="${__internal_key}_help"
)

source $__parse_script_dir/defaults.sh

declare -A __default_help_opt=([short]="h" [long]="help" [help]="Display this help message")
declare -A __default_dry_run_opt=([long]="dry-run" [help]="Print the commands that would be executed for this script")
declare -A __default_url_opt=([short]="u" [long]="url" [arg]=true [default]=$DEFAULT_URL [help]="The portainer server base URL")
declare -A __default_username_opt=([short]="U" [long]="username" [arg]=true [default]=$DEFAULT_USERNAME [help]="The admin username")
declare -A __default_password_opt=([short]="P" [long]="password" [arg]=true [default]=$DEFAULT_PASSWORD [help]="The admin password")

declare __internal_default_keys=("help" "dry-run" "url" "username" "password")

# extract only long options from the options
function extract_long_options() {
  local -n __opts="$1"

  local -A __long_opts
  for k in ${!__opts[@]}; do
    if [[ ! $k =~ ^${__internal_key} ]] && [[ ${#k} -ne 1 ]]; then
      __long_opts+=("$k")
    fi
  done

  echo "${!__long_opts[@]}" | tr ' ' '\n' | sort
}

# saves (
#   [short]=string                        # required - errors if empty
#   [long]=string                         # required - errors if empty
#   [required]=?bool                      # optional - defaults to false
#   [arg]=?bool                           # optional - defaults to false
#   [default]=?any                        # optional - defaults to false
#   [help]="help display"                 # optional - defaults to ""
# )
# into $__opts as (
#   __opts[$short]=$long                      # mapping of short name to long name of option
#   __opts[$long]=$short                      # mapping of long name to short name of option
#
#   __opts[required_$long]=true|false         # save of "required_$long" (required_alpha) to retain option is required
#   __opts[arg_$long]=true|false              # save of "arg_$long" (arg_alpha) to retain option requiring an argument
#   __opts[default_$long]=default ?? false    # save of "default_$long" (default_alpha) to retain default value of option
#   __opts[help_$long]=help ?? ""             # save of "help_$long" (help_alpha) to retain usage information of help
# )
function register_option() {
  local __opts_ref="$1"
  local __opt_ref="$2"
  local -n __opts=$__opts_ref
  local -n __opt=$__opt_ref

  if ! declare -p "$__opts_ref" &>/dev/null; then
    die "Error: register_option -- unknown variable for array of options '$__opts_ref'"
  fi

  if ! declare -p "$__opt_ref" &>/dev/null; then
    die "Error: register_option -- unknown variable for option '$__opt_ref'"
  fi

  local short="${__opt[short]:-${__opt[s]:-""}}"
  local long="${__opt[long]:-${__opt[l]:?"Missing 'long' key for option '$__opt_ref'"}}"
  local with_arg="${__opt[arg]:-${__opt[a]:-false}}"
  local default="${__opt[default]:-${__opt[d]:-false}}"
  local required="${__opt[required]:-${__opt[r]:-false}}"
  local help="${__opt[help]:-${__opt[h]:-""}}"

  if [[ $short == *" "* ]] || [[ $long == *" "* ]]; then
    die "Error: option keys must not contain spaces -- (short) '$short' - (long) '$long'"
  fi

  if [[ ${#long} -le 1 ]]; then
    die "Error: long arg for option must contain at least 2 characters -- '${long}'"
  fi

  # sanity check if long option already exists
  if [[ -n ${__opts[$long]+x} ]]; then
    die "Error: option already exists -- '${long}'"
  fi

  # sanity check if short option exists and has already been registered
  if [[ -n $short && -n ${__opts[$short]+x} ]]; then
    # allow overriding short keys of default options
    local known_long=${__opts[$short]}
    if arr_contains __internal_default_keys $known_long; then
      __opts[${__opts[$short]}]=""
    else
      die "Error: short option '${short}' for '${long}' already exists for option '${__opts[$short]}'"
    fi
  fi

  __opts[$long]="$short"
  if [[ -n $short ]]; then
    __opts[$short]="$long"
  fi

  # save the option specifics
  __opts[${__internal_keys[required]}_${long}]=$required
  __opts[${__internal_keys[arg]}_${long}]=$with_arg
  __opts[${__internal_keys[default]}_${long}]=$default
  __opts[${__internal_keys[help]}_${long}]=$help
}

# build the `getopt` string from the registered options
function build_getopt_strings() {
  local __opts_ref="$1"
  local -n __opts="$1"

  local __sorted_keys=($(extract_long_options $__opts_ref))
  for k in ${__sorted_keys[@]}; do

    local long=$k
    local short=${__opts[$k]}
    local with_arg=${__opts[${__internal_keys[arg]}_${long}]}

    local arg_char=""
    if [[ $with_arg == true ]]; then
      arg_char=":"
    fi

    __opts[${__internal_keys[long_opts]}]+="${long}${arg_char},"
    if [[ -n "$short" ]]; then
      __opts[${__internal_keys[short_opts]}]+="${short}${arg_char}"
    fi
  done
}

# register all options at once
# MUST PASS VARIABLE AS NAMEREF USING "var_name" AND NOT USING $var
# First argument must be the options table to save the options to
#
# Example: see top of this file
function register_options() {
  local __opts_ref="$1"
  shift

  # register default options
  register_option $__opts_ref __default_help_opt
  register_option $__opts_ref __default_dry_run_opt
  register_option $__opts_ref __default_url_opt
  register_option $__opts_ref __default_username_opt
  register_option $__opts_ref __default_password_opt

  # register user-provided options
  for __opt_var in "$@"; do
    register_option "$__opts_ref" "$__opt_var"
  done
}

# remove the specified options
# used mostly when the script leveraging the options package
# doesn't use the axios package and wants to hide some of the default options
function remove_options() {
  local __opts_ref="$1"
  local -n __opts=$__opts_ref
  shift

  for __opt in "$@"; do
    if [[ ${#__opt} -le 1 ]]; then
      die "Error: remove option by using its long name -- '$__opt'"
    fi
    if [[ -z "${__opts[$__opt]+x}" ]]; then
      die "Error: unknown key to remove -- '$__opt'"
    fi
    local short="${__opts[$__opt]}"
    unset "__opts[$short]"
    unset "__opts[$__opt]"
  done
}

# prints the option details (help format)
function print_opt_details() {
  local -n __opts="$1"
  local k=$2

  local long=$k
  local short=${__opts[$k]}
  local required=${__opts[${__internal_keys[required]}_${long}]}
  local arg=${__opts[${__internal_keys[arg]}_${long}]}
  local default=${__opts[${__internal_keys[default]}_${long}]}
  local help=${__opts[${__internal_keys[help]}_${long}]}

  local more=""
  if [[ $arg = true ]]; then
    more="string"
  fi

  if [[ ! $required = true && ! $long = ${__default_help_opt[long]} && ! $long = ${__default_dry_run_opt[long]} ]] &&
    ([[ $arg = false || ! $default = false ]]); then
    more+="\t(default: $default)"
  fi

  if [[ -z $short ]]; then
    echo -e "\t     --$long $more"
  else
    echo -e "\t -$short, --$long $more"
  fi

  if [[ -n "$help" ]]; then
    echo -e "$help" | sed "s/^/\t\t/"
  fi

  echo -e ""
}

# prints the usage from saved options help
function print_usage() {
  local __opts_ref="$1"
  local -n __opts="$__opts_ref"

  local sorted_keys=($(extract_long_options $__opts_ref))

  # split required and optional options
  local -A required_opts=()
  local -A optional_opts=()
  for k in ${sorted_keys[@]}; do
    if [[ ${__opts[${__internal_keys[required]}_${k}]} = true ]]; then
      required_opts+=("$k")
    else
      optional_opts+=("$k")
    fi
  done

  echo -e "\nUsage: $0\n"

  if [[ ${#required_opts[@]} -ne 0 ]]; then
    echo -e "Required options\n"
    for k in $(extract_long_options required_opts); do
      print_opt_details $__opts_ref "$k"
    done
  fi

  if [[ ${#optional_opts[@]} -ne 0 ]]; then
    echo -e "Optional options\n"
    for k in $(extract_long_options optional_opts); do
      print_opt_details $__opts_ref "$k"
    done
  fi
}

# validate that required options have been provided
function validate_required_args() {
  local __opts_ref="$1"
  local -n __opts="$__opts_ref"

  local sorted_keys=($(extract_long_options $__opts_ref))

  local -A errors=()

  for k in ${sorted_keys[@]}; do
    local required=${__opts[${__internal_keys[required]}_${k}]}
    local seen=${__opts[${__internal_keys[seen]}_${k}]:-false}

    if [[ $required = true ]] && [[ $seen = false ]]; then
      errors+=("$k")
    fi
  done

  if [[ ${#errors[@]} -ne 0 ]]; then
    local missing=""
    for k in ${!errors[@]}; do
      missing+=" '$k'"
    done

    print_usage $__opts_ref
    print_error "$0: missing required options --$missing\n"
    die ""
  fi
}

# populate the default values of all options in the args array
function populate_default_values() {
  local -n __opts="$1"
  local -n __args="$2"

  for k in "${!__opts[@]}"; do

    # ignore internal keys
    if [[ ! $k =~ ^${__internal_key} ]]; then

      # try to get the default for the key
      local default=${__opts[${__internal_keys[default]}_${k}]:-false}

      # get the default value from the long form of the option when we evaluate the short form
      if [[ ${#k} -eq 1 ]]; then
        local long=${__opts[$k]}
        default=${__opts[${__internal_keys[default]}_${long}]}
      fi

      __args[$k]=$default
    fi
  done
}

# parse args of the script according to saved options and populates the args variable
#
# Example: see top of this file
function parse_args() {
  local __opts_ref="$1"
  local __args_ref="$2"
  local __positional_ref="$3"
  shift 3

  local -n __opts=$__opts_ref
  local -n __args=$__args_ref
  local -n __positional=$__positional_ref

  build_getopt_strings $__opts_ref

  local short_opts=${__opts[${__internal_keys[short_opts]}]}
  local long_opts=${__opts[${__internal_keys[long_opts]}]}

  # Use getopt to parse the options
  local VALID_ARGS
  VALID_ARGS=$(getopt -o "$short_opts" --long "$long_opts" --name "$0" -- "$@")
  if [[ $? -ne 0 ]]; then
    die
  fi

  # Reorder the arguments as parsed by getopt
  eval set -- "$VALID_ARGS"

  populate_default_values $__opts_ref $__args_ref

  # Loop through the arguments
  while [ "$#" -gt 0 ]; do
    case "$1" in
    --)
      shift
      break
      ;;
    -*)
      # splice the first 2 hyphens of the arg if they exist
      local opt="${1#-}"
      opt="${opt#-}"
      shift

      local long=$opt
      # retrieve long option from short option (-a -> --alpha)
      if [[ ${#opt} -eq 1 ]]; then
        long="${__opts[$opt]}"
      fi

      if [[ $long = "help" ]]; then
        print_usage $__opts_ref
        exit 0
      fi

      # save long value (default to "switch value encountered = true")
      __args[$long]=true

      # check if the option accepts an argument then extract and save it
      if [[ ${__opts[${__internal_keys[arg]}_${long}]} = true ]]; then
        __args[$long]="${1}"
        shift
      fi

      # track seen options and later check for required ones
      __opts[${__internal_keys[seen]}_${long}]=true
      ;;
    *)
      echo "Unhandled positional argument: $1"
      shift
      ;;
    esac
  done

  validate_required_args "$__opts_ref"

  __positional+=("$@")
}
