#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

dir=$(dirname -- "${BASH_SOURCE[0]}")

# imports (relative from the script dir, not the execution location)
source $dir/utils/defaults.sh
source $dir/utils/axios.sh
source $dir/utils/options.sh

source $dir/steps/create_edge_group.sh
source $dir/steps/get_edge_group.sh
source $dir/steps/trust_envs.sh

declare -A options
declare -A log_level_opt=([short]="l" [long]="log-level" [arg]=true [default]="INFO" [help]="log level")
declare -A log_mode_opt=([short]="m" [long]="log-mode" [arg]=true [default]="PRETTY" [help]="log mode")
declare -A img_opt=([short]="i" [long]="image" [arg]=true [default]="portainerci/hollow-agent:develop" [help]="The hollow-agent image to use")
declare -A use_bin_opt=([long]="use-binary" [help]="When true, spawn the hollow-agent using the binary specified by the -b, --binary option instead of using containers")
declare -A bin_opt=([short]="b" [long]="binary" [arg]=true [default]="${dir}/../dist/hollow-agent" [help]="The hollow-agent binary to use. Has no effect when --use-binary is unset")
declare -A config_opt=([short]="c" [long]="config" [arg]=true [required]="true"
  [help]="Config file describing the agents to deploy.
JSON format: array [] of
{
  "platform": docker|podman,
  "polling_interval": number,
  "env_count": number,
  "groups_count": number,
  "stacks_count": number,
  "env_per_group": number,
  "async_percent": number (0 - 100)
}
"
)

register_options options log_level_opt log_mode_opt img_opt use_bin_opt bin_opt config_opt

declare -A args
declare -a pos_args
parse_args options args pos_args $@

declare url username password
arr_unpack_associative args url username password

init_axios -u "$url" -U "$username" -P "$password"

dry_run=${args["dry-run"]}
use_binary=${args["use-binary"]}

declare -a keys=("platform" "polling_interval" "env_count" "groups_count" "stacks_count" "env_per_group" "async_percent")

function validate_vars() {
  local json_ref="$1"
  local -n __values="$2"

  local "${keys[@]}"
  arr_unpack_indexed __values "${keys[@]}"

  for var_ref in "${keys[@]}"; do
    local -n var=$var_ref
    if [[ $var = "null" ]]; then
      die "$(echo "$json_ref" | jq .)\n$var_ref must not be empty"
    fi
  done
}

function deploy_ha() {
  local -n __values="$1"

  # unpack json config values (saved into __values) into local vars
  local "${keys[@]}"
  arr_unpack_indexed __values "${keys[@]}"

  echo " >> Creating the default edge group"
  # create default edge group or retrieve its Id
  local default_group_name="hollow-agent_default_group"
  local default_id
  default_id=$(create_edge_group -n $default_group_name | jq -r '.Id' || die)
  if [[ "$default_id" == "null" ]]; then
    default_id=$(get_edge_group -n $default_group_name || die)
  fi

  # create edge groups
  echo " >> Creating $groups_count edge groups"
  local ids
  for i in $(seq 1 $groups_count); do
    local name=$(random_name)

    local result
    result=$(create_edge_group -n "$name" || die)
    ids+=("$result | jq -r '.Id'")

    # local cmd=("create_edge_group" "-n" "$name")
    # local result=$(run_or_print "${cmd[@]}")
    # if [[ $dry_run = false ]]; then
    #   ids+=("$result | jq -r '.Id'")
    # else
    #   echo "$result"
    # fi
  done

  # create envs and assign them to the default group
  local async_count=$(($env_count * $async_percent / 100))
  local non_async_count=$(($env_count - $async_count))
  local sync_pid
  local async_pid

  if [[ $non_async_count -ne 0 ]]; then
    echo " >> Creating $non_async_count standard agents"
    local cmd=("docker" "run" "-d" "--name" "hollow-agent_$(random_name)" "${args[image]}")
    if [[ $use_binary == true ]]; then
      cmd=(${args[binary]})
    fi

    cmd+=(
      "--log-level" "${args["log-level"]}"
      "--log-mode" "${args["log-mode"]}"
      "--server" "${args["url"]}"
      "--edge-group-id" "$default_id"
      "--polling-interval" "$polling_interval"
      "--container-platform" "$platform"
      "--num-agents" "$non_async_count"
      "--edge-insecurepoll"
    )

    if [[ $use_binary == true ]]; then
      "${cmd[@]}" &
    else
      "${cmd[@]}"
    fi
    sync_pid=$!
  fi

  if [[ $async_count -ne 0 ]]; then
    echo " >> Creating $async_count async agents"
    # start async agents
    local cmd=("docker" "run" "-d" "--name" "hollow-agent_$(random_name)" "${args[image]}")
    if [[ $use_binary == true ]]; then
      cmd=(${args[binary]})
    fi

    cmd+=(
      "--log-level" "${args["log-level"]}"
      "--log-mode" "${args["log-mode"]}"
      "--server" "${args["url"]}"
      "--edge-group-id" "$default_id"
      "--polling-interval" "$polling_interval"
      "--container-platform" "$platform"
      "--num-agents" "$async_count"
      "--async-mode"
      "--edge-insecurepoll"
    )

    if [[ $use_binary == true ]]; then
      "${cmd[@]}" &
    else
      "${cmd[@]}"
    fi
    async_pid=$!
  fi

  if [[ $dry_run = false && $use_binary == true ]]; then
    echo " >> Trapping SIGINT. Use ctrl+C to kill the hollow-agent processes -- sync_pid=[$sync_pid] async_pid=[$async_pid]"

    # Define a trap to handle SIGINT (Ctrl+C) and kill background processes
    trap "echo ' >> SIGINT caught, killing processes'; kill $sync_pid $async_pid" SIGINT

    # trust envs
    # randomly add envs to each group to match $env_per_group

    ## experiment with "env_ratio:group_ratio" so each agent join random groups
    # local env_ratio group_ratio
    # IFS=":" read -r env_ratio group_ratio <<<"${env_group_ratio}"

    # Wait for the background processes to finish (this keeps the script running)
    [[ -n $sync_pid ]] && wait $sync_pid
    [[ -n $async_pid ]] && wait $async_pid
  fi

  echo " >> Done."
}

function run() {
  # prepend keys with a dot (.) and join them with a comma (,)
  local jq_eval=$(
    IFS=,
    echo "${keys[*]/#/.}"
  )

  # for each config in the config file
  for k in $(jq -c '.[]' "${args[config]}"); do
    local -a values
    # extract values of single into the __vars array
    mapfile -t values < <(echo $k | jq -r "$jq_eval")

    validate_vars $k values

    deploy_ha values

  done

}

run
