#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_ENABLE_EDGE_COMPUTE:-}" ]] && return
__SOURCED_SCRIPT_ENABLE_EDGE_COMPUTE=true

function enable_edge_compute() {

  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  # imports (relative from the script dir, not the execution location)
  source $dir/../utils/defaults.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/options.sh
  source $dir/../utils/utils.sh

  local -A options
  local -A portainer_opt=([short]="p" [long]="portainer-url" [arg]=true [required]=true [help]="The API URL to apply")
  local -A tunnel_opt=([short]="t" [long]="tunnel-url" [arg]=true [required]=true [help]="The tunnel URL to apply")
  register_options options portainer_opt tunnel_opt

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local portainer=${args["portainer-url"]}
  local tunnel=${args["tunnel-url"]}

  local payload=$(
    jq -n \
      --arg portainer_url $portainer \
      --arg tunnel_url $tunnel \
      '{
      EnableEdgeComputeFeatures:true,
      EdgePortainerUrl: $portainer_url,
      Edge:{ TunnelServerAddress: $tunnel_url },
    }'
  )

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  put "$url/api/settings" "$payload"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  enable_edge_compute $@
fi
