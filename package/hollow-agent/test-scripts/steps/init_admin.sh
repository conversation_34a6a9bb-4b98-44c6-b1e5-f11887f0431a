#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_INIT_ADMIN:-}" ]] && return
__SOURCED_SCRIPT_INIT_ADMIN=true

function init_admin() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  # imports (relative from the script dir, not the execution location)
  source $dir/../utils/defaults.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/options.sh

  local -A options
  register_options options

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local url=${args[url]}
  local username=${args[username]}
  local password=${args[password]}

  local payload=$(
    jq -n \
      --arg username $username \
      --arg password $password \
      '{ Username: $username, Password: $password }'
  )

  echo -e " >> Creating initial admin [ $username ] [ $password ]"
  curl --json "$payload" "$url/api/users/admin/init"
  echo -e "Done."
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  init_admin $@
fi
