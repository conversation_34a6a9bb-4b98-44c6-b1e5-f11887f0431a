#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_TRUST_ENVS:-}" ]] && return
__SOURCED_SCRIPT_TRUST_ENVS=true

function trust_envs() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  source $dir/../utils/options.sh
  source $dir/../utils/defaults.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/utils.sh

  local -A options
  register_options options

  local -A args
  local -a ids
  parse_args options args ids $@

  local payload=$(
    jq -n \
      --argjson ids "$(arr_to_json ids)" \
      '{ EndpointIDs: $ids }'
  )

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  echo -e "Trusting envs: [${ids[@]}]"
  post "$url/api/endpoints/edge/trust" "$payload"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  trust_envs $@
fi
