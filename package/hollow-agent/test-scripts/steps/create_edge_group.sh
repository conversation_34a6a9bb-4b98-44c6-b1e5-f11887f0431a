#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_CREATE_EDGE_GROUP:-}" ]] && return
__SOURCED_SCRIPT_CREATE_EDGE_GROUP=true

function create_edge_group() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  source $dir/../utils/options.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/utils.sh

  local -A options
  local -A name_opt=([short]="n" [long]="name" [arg]=true [required]=true)
  local -A dynamic_opt=([short]="d" [long]="dynamic")
  local -A partial_opt=([short]="p" [long]="partial-match")
  register_options options name_opt dynamic_opt partial_opt

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local dry_run=${args["dry-run"]}

  local name=${args[name]}
  local dynamic=${args[dynamic]}
  local partial=${args["partial-match"]}

  local -a tags=()

  local payload=$(
    jq -n \
      --arg name $name \
      --argjson endpoints "$(arr_to_json pos_args)" \
      --argjson dynamic $dynamic \
      --argjson partial $partial \
      --argjson tags "$(arr_to_json tags)" \
      '{
        name: $name,
        endpoints: $endpoints,
        dynamic: $dynamic,
        partialMatch: $partial,
        tagIds: $tags
      }'
  )

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  post "$url/api/edge_groups" "$payload"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  create_edge_group $@
fi
