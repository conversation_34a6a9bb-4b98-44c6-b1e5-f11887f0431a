#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_INIT_LICENSE:-}" ]] && return
__SOURCED_SCRIPT_INIT_LICENSE=true

function init_license() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  # imports (relative from the script dir, not the execution location)
  source $dir/../utils/defaults.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/options.sh
  source $dir/../utils/utils.sh

  local -A options
  local -A key_opt=([short]="k" [long]="key" [arg]=true [default]=$DEFAULT_KEY)
  register_options options key_opt

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local key=${args[key]}

  local payload=$(
    jq -n \
      --arg key $key \
      '{ key: $key }'
  )

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  echo " >> Adding license "
  post "$url/api/licenses/add?force=true" "$payload"
  echo -e "Done."
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  init_license $@
fi
