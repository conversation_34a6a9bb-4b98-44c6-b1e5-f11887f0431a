#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_CREATE_EDGE_STACK:-}" ]] && return
__SOURCED_SCRIPT_CREATE_EDGE_STACK=true

function create_edge_stack() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  source $dir/../utils/options.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/utils.sh

  local -A options
  local -A name_opt=([short]="n" [long]="name" [arg]=true [required]=true)
  local -A prepull_image_opt=([long]="prepull-image")
  local -A retry_deploy_opt=([long]="retry-deploy")
  # local -A deployment_type_opt=([long]="deployment-type" [arg]=true [default]="docker")
  local -A stack_type_opt=([long]="stack-type" [arg]=true [default]="editor")
  register_options options name_opt prepull_image_opt

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local name=${args[name]}
  local full_url
  local payload

  local deployment_type=${args["deployment-type"]}
  local -a valid_types=("editor" "git")
  case "$stack_type" in
  editor)
    $deployment_type=0
    full_url="$url/api/edge_stacks/create/string"
    pay
    ;;
  git)
    $deployment_type=2
    full_url="$url/api/edge_stacks/create/git"
    payload=$(
      jq -n \
      '{
        "deploymentType":0,
        "edgeGroups":[2],
        "name":"test2",
        "envVars":[],
        "prePullImage":false,
        "registries":[],
        "retryDeploy":false,
        "retryPeriod":600,
        "staggerConfig": {
          "StaggerOption":1,
          "StaggerParallelOption":1,
          "DeviceNumber":1,
          "DeviceNumberStartFrom":0,
          "DeviceNumberIncrementBy":2,
          "Timeout":"",
          "UpdateDelay":"",
          "UpdateFailureAction":1
        },
        "useManifestNamespaces":false,
        "repositoryUrl":"https://github.com/xAt0mZ/portainer-git-feats-test-files",
        "repositoryReferenceName":"refs/heads/master",
        "filePathInRepository":"stacks/nginx.yml",
        "repositoryAuthentication":false,
        "filesystemPath":"",
        "supportRelativePath":false,
        "supportPerDeviceConfigs":false,
        "perDeviceConfigsGroupMatchType":"",
        "perDeviceConfigsMatchType":"",
        "perDeviceConfigsPath":"",
        "tlsSkipVerify":false,
        "autoUpdate":null
      }'
    )
    ;;
  *)
    die "Error: unknown deployment type -- '$deployment_type'"
    ;;
  esac

  local payload=$(
    jq -n \
      --arg name $name \
      --argjson edge_groups "$(arr_to_json pos_args)" \
      --argjson prepull_image "$prepull_image" \
      --argjson retry_deploy "$retry_deploy" \
      '{
        deploymentType: 0,
        edgeGroups: $edge_groups,
        name: $name,
        envVars: [],
        prePullImage: $prepull_image,
        registries: [],
        retryDeploy:  $retry_redeploy,
        retryPeriod: 600,
        staggerConfig: {
          StaggerOption: 1,
          StaggerParallelOption: 1,
          DeviceNumber: 1,
          DeviceNumberStartFrom: 0,
          DeviceNumberIncrementBy: 2,
          Timeout: "",
          UpdateDelay: "",
          UpdateFailureAction: 1
        },
        useManifestNamespaces: false,
        stackFileContent: "services:\n  nginx:\n    image: nginx"
      }'
  )

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  post "$url/api/edge_stacks/create/string" "$payload"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  create_edge_stack $@
fi
