#!/bin/bash

# strict mode - based on http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail
IFS=$'\n\t'

[[ -n "${__SOURCED_SCRIPT_GET_EDGE_GROUP:-}" ]] && return
__SOURCED_SCRIPT_GET_EDGE_GROUP=true

function get_edge_group() {
  local dir=$(dirname -- "${BASH_SOURCE[0]}")

  source $dir/../utils/options.sh
  source $dir/../utils/defaults.sh
  source $dir/../utils/axios.sh
  source $dir/../utils/utils.sh

  local -A options
  local -A name_opt=([short]="n" [long]="name" [arg]=true [required]=true)
  register_options options name_opt

  local -A args
  local -a pos_args
  parse_args options args pos_args $@

  local url username password
  arr_unpack_associative args url username password

  init_axios -u "$url" -U "$username" -P "$password"

  local name=${args[name]}
  get "$url/api/edge_groups" | jq -r ".[] | select(.Name == \"$name\") | .Id"
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  get_edge_group $@
fi
