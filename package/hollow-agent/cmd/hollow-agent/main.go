package main

import (
	"sync"
	"time"

	"github.com/portainer/agent"
	hollowagent "github.com/portainer/hollow-agent"
	"github.com/portainer/hollow-agent/agents"
	"github.com/portainer/portainer/api/logs"
	"github.com/portainer/portainer/pkg/fips"
	"github.com/portainer/portainer/pkg/librand"

	"github.com/alecthomas/kingpin/v2"
	"github.com/rs/zerolog/log"
)

func parseFlags() *hollowagent.Flags {
	defer kingpin.Parse()

	return &hollowagent.Flags{
		LogLevel: kingpin.
			Flag("log-level", "log level").
			Default("INFO").Envar(hollowagent.EnvVarLogLevel).String(),
		LogMode: kingpin.
			Flag("log-mode", "log mode").
			Default("PRETTY").Envar(hollowagent.EnvVarLogMode).String(),
		NumAgents: kingpin.
			Flag("num-agents", "number of agents to run concurrently").
			Default("100").Envar(hollowagent.EnvVarNumAgents).Int(),
		PortainerAddr: kingpin.
			Flag("server", "Portainer server address").
			Default("https://127.0.0.1:9443").Envar(hollowagent.EnvVarPortainerAddr).String(),
		EdgeGroupID: kingpin.
			Flag("edge-group-id", "edge group ID for the Agent to join").
			Default("1").Envar(hollowagent.EnvVarEdgeGroupID).Int(),
		PollingInterval: kingpin.
			Flag("polling-interval", "polling interval in seconds").
			Default("5").Envar(hollowagent.EnvVarPollingInterval).Int(),
		AsyncMode: kingpin.
			Flag("async-mode", "async agent").
			Default("false").Envar(hollowagent.EnvVarAsyncMode).Bool(),
		ContainerPlatform: kingpin.
			Flag("container-platform", "container platform").
			Default("docker").Envar(hollowagent.EnvVarContainerPlatform).String(),
		IPAddrs: kingpin.
			Flag("ip-addr", "source IP address for the agents, can be specified multiple times").
			Envar(hollowagent.EnvVarIPAddrs).IPList(),
		EdgeInsecurePoll: kingpin.
			Flag("edge-insecurepoll", hollowagent.EnvKeyEdgeInsecurePoll+" enable this option if you need the agent to poll a HTTPS Portainer instance with self-signed certificates. Disabled by default, set to 1 to enable it").
			Envar(hollowagent.EnvKeyEdgeInsecurePoll).Bool(),
		MTLSCert: kingpin.
			Flag("mtlscert", "Path to the mTLS certificate used to identify the agent to Portainer").
			Envar(hollowagent.EnvVarMTLSCert).String(),
		MTLSKey: kingpin.
			Flag("mtlskey", "Path to the mTLS key used to identify the agent to Portainer").
			Envar(hollowagent.EnvVarMTLSKey).String(),
		MTLSCACert: kingpin.
			Flag("mtlscacert", "path to the CA certificate file for mTLS").
			Default("").Envar(hollowagent.EnvVarMTLSCACert).String(),
		AgentVersion: kingpin.
			Flag("agent-version", "simulated version of the agents. Use this option to for remote updates/rollbacks").
			Default(agent.Version).Envar(hollowagent.EnvVarAgentVersion).String(),
		GroupID: kingpin.
			Flag("environment-group", "group ID for the Agent to join").
			Default("1").Envar(hollowagent.EnvVarEndpointGroup).Int(),
		TagIDs: kingpin.
			Flag("tags", "a colon-separated list of tags to associate to the environment").
			Default("").Envar(hollowagent.EnvVarTags).String(),
		StartupDelay: kingpin.
			Flag("startup-delay", "(in seconds). Define the max value of the ]0 ; <startup-delay>] range from which a random delay will be chosen for each agent, to avoid starting all of them at the same time").
			Default("60").Envar(hollowagent.EnvVarStartupDelay).Int(),
		EmptySnapshot: kingpin.
			Flag("empty-snapshot", "when true, send empty Docker snapshots").
			Envar(hollowagent.EnvVarEmptySnapshot).Bool(),
	}
}

func main() {
	fips.InitFIPS(false)
	logs.ConfigureLogger()
	logs.SetLoggingMode("PRETTY")

	flags := parseFlags()

	logs.SetLoggingLevel(*flags.LogLevel)
	logs.SetLoggingMode(*flags.LogMode)

	log.Info().Int("NumOfAgents", *flags.NumAgents).
		Str("Server", *flags.PortainerAddr).
		Int("EdgeGroupId", *flags.EdgeGroupID).
		Int("PollingInterval", *flags.PollingInterval).
		Bool("AsyncMode", *flags.AsyncMode).
		Str("ContainerPlatform", *flags.ContainerPlatform).
		Any("IPAddrs", *flags.IPAddrs).
		Bool("EdgeInsecurePoll", *flags.EdgeInsecurePoll).
		Str("MTLSCert", *flags.MTLSCert).
		Str("MTLSKey", *flags.MTLSKey).
		Str("MTLSCACert", *flags.MTLSCACert).
		Str("AgentVersion", *flags.AgentVersion).
		Int("GroupID", *flags.GroupID).
		Str("TagIDs", *flags.TagIDs).
		Int("StartupDelay", *flags.StartupDelay).
		Bool("EmptySnapshot", *flags.EmptySnapshot).
		Msg("Starting Hollow Agent. Parsing flags")

	wg := &sync.WaitGroup{}
	wg.Add(*flags.NumAgents)

	startWg := &sync.WaitGroup{}
	startWg.Add(*flags.NumAgents)
	for i := range *flags.NumAgents {
		// Random delay to avoid all agents starting at the same time, to better simulate real-world scenarios
		delay := 0 * time.Millisecond
		if *flags.StartupDelay > 0 {
			delay = time.Duration(librand.Intn(*flags.StartupDelay*1000)) * time.Millisecond
		}
		go func(delay time.Duration) {
			time.Sleep(delay)
			if *flags.AsyncMode {
				go agents.CreateEdgeAsyncAgent(wg, i, flags)
			} else {
				go agents.CreateEdgeAgent(wg, i, flags)
			}
			startWg.Done()
		}(delay)
	}

	startWg.Wait()
	log.Info().Msg("Started all agents")

	wg.Wait()

	log.Info().Msg("Hollow Agent has finished.")
}
