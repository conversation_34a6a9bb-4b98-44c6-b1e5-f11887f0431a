<page-header
  ng-if="ctrl.state.viewReady"
  title="'Secret details'"
  breadcrumbs="[
    { label:'Namespaces', link:'kubernetes.resourcePools' },
    {
      label:ctrl.configuration.Namespace,
      link: 'kubernetes.resourcePools.resourcePool',
      linkParams:{ id: ctrl.configuration.Namespace }
    },
    { label:'ConfigMaps and Secrets', link:'kubernetes.configurations', linkParams:{ tab: 'secrets' } },
    ctrl.configuration.Name,
     ]"
  reload="true"
>
</page-header>

<kubernetes-view-loading view-ready="ctrl.state.viewReady"></kubernetes-view-loading>

<div ng-if="ctrl.state.viewReady">
  <div class="row">
    <div class="col-sm-12">
      <rd-widget>
        <rd-widget-body classes="no-padding">
          <uib-tabset active="ctrl.state.activeTab" justified="true" type="pills">
            <uib-tab index="0" classes="btn-sm" select="ctrl.selectTab(0)" data-cy="k8sConfigDetail-configTab">
              <uib-tab-heading>
                <pr-icon icon="'lock'"></pr-icon>
                Secret
              </uib-tab-heading>
              <div class="widget-body">
                <table class="table" data-cy="k8sConfigDetail-configTable">
                  <tbody>
                    <tr>
                      <td class="w-[40%] !border-none">Name</td>
                      <td class="!border-none">
                        {{ ctrl.configuration.Name }}
                        <span style="margin-left: 5px" class="label label-info image-tag" ng-if="ctrl.configuration.IsRegistrySecret">system</span>
                      </td>
                    </tr>
                    <tr>
                      <td>Namespace</td>
                      <td>
                        <a ui-sref="kubernetes.resourcePools.resourcePool({ id: ctrl.configuration.Namespace })">{{ ctrl.configuration.Namespace }}</a>
                        <span style="margin-left: 5px" class="label label-info image-tag" ng-if="ctrl.isSystemNamespace()">system</span>
                      </td>
                    </tr>
                    <tr ng-if="ctrl.secretTypeName">
                      <td>Secret Type</td>
                      <td> {{ ctrl.secretTypeName }} </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </uib-tab>
            <uib-tab index="1" classes="btn-sm" select="ctrl.selectTab(1)" data-cy="k8sConfigDetail-eventsTab">
              <uib-tab-heading>
                <pr-icon icon="'history'"></pr-icon>
                Events
                <div ng-if="ctrl.hasEventWarnings()">
                  <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
                  {{ ctrl.state.eventWarningCount }} warning(s)
                </div>
              </uib-tab-heading>
              <resource-events-datatable
                resource-id="ctrl.configuration.Id"
                storage-key="'kubernetes.secret.events'"
                namespace="ctrl.configuration.Namespace"
              ></resource-events-datatable>
            </uib-tab>
            <uib-tab index="2" ng-if="ctrl.configuration.Yaml" classes="btn-sm" select="ctrl.showEditor()" data-cy="k8sConfigDetail-yamlTab">
              <uib-tab-heading>
                <pr-icon icon="'code'"></pr-icon>
                YAML
              </uib-tab-heading>
              <div class="px-5 !pt-5" ng-if="ctrl.state.showEditorTab">
                <kube-yaml-inspector data-cy="k8sConfigDetail-yamlInspector" identifier="'secret-yaml'" data="ctrl.configuration.Yaml" hide-message="true" />
              </div>
            </uib-tab>
          </uib-tabset>
        </rd-widget-body>
      </rd-widget>
    </div>
  </div>

  <div class="row">
    <div class="col-sm-12">
      <rd-widget>
        <rd-widget-body>
          <form ng-if="!ctrl.isSystemConfig()" class="form-horizontal" name="kubernetesConfigurationCreationForm" autocomplete="off">
            <div class="col-sm-12 !p-0">
              <annotations-be-teaser></annotations-be-teaser>
            </div>

            <kubernetes-configuration-data
              ng-if="ctrl.formValues"
              form-values="ctrl.formValues"
              is-docker-config="ctrl.state.isDockerConfig"
              is-valid="ctrl.state.isDataValid"
              on-change-validation="ctrl.isFormValid()"
              is-creation="false"
              type="'secret'"
              is-editor-dirty="ctrl.state.isEditorDirty"
            ></kubernetes-configuration-data>

            <div class="form-group" ng-if="ctrl.state.secretWarningMessage">
              <div class="col-sm-12 small text-warning vertical-center pt-5">
                <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
                <span>{{ ctrl.state.secretWarningMessage }}</span>
              </div>
            </div>

            <!-- summary -->
            <kubernetes-summary-view
              ng-if="!(!ctrl.isFormValid() || !kubernetesConfigurationCreationForm.$valid || ctrl.state.actionInProgress)"
              form-values="ctrl.formValues"
            ></kubernetes-summary-view>

            <!-- actions -->
            <div class="col-sm-12 form-section-title" style="margin-top: 10px"> Actions </div>
            <div class="form-group">
              <div class="col-sm-12">
                <button
                  type="button"
                  class="btn btn-primary btn-sm !ml-0"
                  ng-disabled="!ctrl.isFormValid() || !kubernetesConfigurationCreationForm.$valid || ctrl.state.actionInProgress"
                  ng-click="ctrl.updateConfiguration()"
                  button-spinner="ctrl.state.actionInProgress"
                  data-cy="k8sConfigDetail-updateConfig"
                >
                  <span ng-hide="ctrl.state.actionInProgress">Update {{ ctrl.configuration.Kind | kubernetesConfigurationKindText }}</span>
                  <span ng-show="ctrl.state.actionInProgress">Update in progress...</span>
                </button>
              </div>
            </div>
            <!-- !actions -->
          </form>
          <div ng-if="ctrl.isSystemConfig()">
            <div class="col-sm-12 form-section-title" style="margin-top: 10px"> Data </div>
            <table class="table">
              <tbody>
                <tr class="text-muted">
                  <td style="width: 10%; border-top: none">Key</td>
                  <td style="width: 90%; border-top: none">Value</td>
                </tr>

                <tr ng-repeat="item in ctrl.formValues.Data track by $index">
                  <td>{{ item.Key }}</td>
                  <td>
                    <div style="white-space: pre-wrap">{{ item.Value }}</div>
                    <div style="margin-top: 2px">
                      <span class="btn btn-primary btn-xs" ng-click="ctrl.copyConfigurationValue($index)"> <pr-icon icon="'copy'" class-name="'mr-0.5'"></pr-icon>Copy </span>
                      <span id="copyValueNotification_{{ $index }}" style="display: none; color: #23ae89; margin-left: 5px" class="small">
                        <pr-icon icon="'check'"></pr-icon> copied
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </rd-widget-body>
      </rd-widget>
    </div>
  </div>

  <kubernetes-integrated-applications-datatable
    ng-if="ctrl.configuration.Used"
    dataset="ctrl.configuration.Applications"
    is-loading="ctrl.state.applicationsLoading"
    table-key="'kubernetes.secrets.applications'"
    on-refresh="(ctrl.getApplications)"
    table-title="'Applications using this secret'"
  >
  </kubernetes-integrated-applications-datatable>
</div>
