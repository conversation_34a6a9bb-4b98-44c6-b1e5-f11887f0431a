<page-header
  ng-if="ctrl.state.viewReady"
  title="'Volume details'"
  breadcrumbs="[
    { label:'Namespaces', link:'kubernetes.resourcePools' },
    {
      label:ctrl.volume.ResourcePool.Namespace.Name,
      link: 'kubernetes.resourcePools.resourcePool',
      linkParams:{ id: ctrl.volume.ResourcePool.Namespace.Name }
    },
    { label:'Volumes', link:'kubernetes.volumes' },
     ctrl.volume.PersistentVolumeClaim.Name,
     ]"
  reload="true"
>
</page-header>

<kubernetes-view-loading view-ready="ctrl.state.viewReady"></kubernetes-view-loading>

<div ng-if="ctrl.state.viewReady">
  <div class="row">
    <div class="col-sm-12">
      <rd-widget>
        <rd-widget-body classes="no-padding">
          <uib-tabset active="ctrl.state.activeTab" justified="true" type="pills">
            <uib-tab index="0" classes="btn-sm" select="ctrl.selectTab(0)">
              <uib-tab-heading class="vertical-center" data-cy="k8sVolDetail-volTab">
                <pr-icon icon="'database'"></pr-icon>
                Volume
              </uib-tab-heading>
              <div class="widget-body">
                <table class="table">
                  <tbody>
                    <tr>
                      <td>Name</td>
                      <td data-cy="k8sVolDetail-volName">
                        {{ ctrl.volume.PersistentVolumeClaim.Name }}
                        <span class="label label-primary image-tag label-margins" ng-if="!ctrl.isSystemNamespace() && ctrl.isExternalVolume()">external</span>
                        <span class="label label-warning image-tag label-margins" ng-if="!ctrl.isSystemNamespace() && !ctrl.isUsed()">unused</span>
                      </td>
                    </tr>
                    <tr>
                      <td>Namespace</td>
                      <td>
                        <a ui-sref="kubernetes.resourcePools.resourcePool({ id: ctrl.volume.ResourcePool.Namespace.Name })" data-cy="k8sVolDetail-volNamespace">{{
                          ctrl.volume.ResourcePool.Namespace.Name
                        }}</a>
                        <span style="margin-left: 5px" class="label label-info image-tag" ng-if="ctrl.isSystemNamespace()">system</span>
                      </td>
                    </tr>
                    <tr>
                      <td>Storage Class</td>
                      <td data-cy="k8sVolDetail-volStorageClassname">{{ ctrl.volume.PersistentVolumeClaim.storageClass.Name }}</td>
                    </tr>
                    <tr>
                      <td>Access Modes</td>
                      <td data-cy="k8sVolDetail-volAccessPolicy">
                        <div ng-repeat="(index, accessPolicy) in ctrl.state.volumeSharedAccessPolicies" class="flex flex-col gap-y-1">
                          <div class="flex items-center gap-x-1">
                            {{ accessPolicy }}
                            <portainer-tooltip
                              ng-if="ctrl.state.volumeSharedAccessPolicyTooltips[index]"
                              message="ctrl.state.volumeSharedAccessPolicyTooltips[index]"
                            ></portainer-tooltip>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td>Provisioner</td>
                      <td data-cy="k8sVolDetail-volProvisioner">{{
                        ctrl.volume.PersistentVolumeClaim.storageClass.Provisioner ? ctrl.volume.PersistentVolumeClaim.storageClass.Provisioner : '-'
                      }}</td>
                    </tr>
                    <tr>
                      <td>Creation date</td>
                      <td data-cy="k8sVolDetail-volCreatedAt">{{ ctrl.volume.PersistentVolumeClaim.CreationDate | getisodate }}</td>
                    </tr>
                    <tr>
                      <td>size</td>
                      <td ng-if="!ctrl.state.increaseSize">
                        {{ ctrl.volume.PersistentVolumeClaim.Storage }}
                        <button
                          type="button"
                          class="btn btn-sm btn-primary"
                          ng-click="ctrl.state.increaseSize = true"
                          ng-if="ctrl.volume.PersistentVolumeClaim.storageClass.AllowVolumeExpansion"
                          data-cy="k8sVolDetail-increaseSizeButton"
                          >Increase size</button
                        >
                      </td>
                      <td ng-if="ctrl.state.increaseSize">
                        <form name="kubernetesVolumeUpdateForm">
                          <div class="form-inline">
                            <div class="input-group input-group-sm">
                              <input
                                type="number"
                                data-cy="k8sVolDetail-increaseSizeInput"
                                class="form-control"
                                name="size"
                                ng-model="ctrl.state.volumeSize"
                                placeholder="20"
                                ng-min="0"
                                min="0"
                                ng-change="ctrl.onChangeSize()"
                                required
                                data-cy="k8sVolDetail-increaseSizeInput"
                              />
                              <span class="input-group-addon" style="padding: 0">
                                <select
                                  ng-model="ctrl.state.volumeSizeUnit"
                                  ng-change="ctrl.onChangeSize()"
                                  ng-options="unit for unit in ctrl.state.availableSizeUnits"
                                  style="width: 100%; height: 100%"
                                  data-cy="k8sVolDetail-increaseSizeUnits"
                                ></select>
                              </span>
                            </div>
                            <button
                              type="button"
                              class="btn btn-sm btn-primary"
                              ng-disabled="!ctrl.sizeIsValid()"
                              ng-click="ctrl.updateVolume()"
                              data-cy="k8sVolDetail-updateSizeConfirm"
                            >
                              Update size
                            </button>
                            <button type="button" class="btn btn-sm btn-default" ng-click="ctrl.state.increaseSize = false" data-cy="k8sVolDetail-cancelUpdateSize">
                              Cancel
                            </button>
                          </div>

                          <div class="form-inline">
                            <div class="small text-warning" style="margin-top: 5px" ng-show="ctrl.state.errors.volumeSize || kubernetesVolumeUpdateForm.size.$invalid">
                              <div class="vertical-center" ng-messages="kubernetesVolumeUpdateForm.size.$error">
                                <p ng-message="required"><pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon> This field is required.</p>
                              </div>
                              <p class="vertical-center" ng-show="ctrl.state.errors.volumeSize"
                                ><pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon> The new size must be greater than the actual size.</p
                              >
                            </div>
                          </div>
                        </form>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </uib-tab>

            <uib-tab index="1" classes="btn-sm" select="ctrl.selectTab(1)">
              <uib-tab-heading class="vertical-center" data-cy="k8sVolDetail-volEventsTab">
                <pr-icon icon="'history'"></pr-icon> Events
                <div ng-if="ctrl.hasEventWarnings()">
                  <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
                  {{ ctrl.state.eventWarningCount }} warning(s)
                </div>
              </uib-tab-heading>
              <resource-events-datatable
                namespace="ctrl.volume.ResourcePool.Namespace.Name"
                storage-key="'kubernetes.volume.events'"
                resource-id="ctrl.volume.PersistentVolumeClaim.Id"
              >
              </resource-events-datatable>
            </uib-tab>

            <uib-tab index="2" ng-if="ctrl.volume.PersistentVolumeClaim.Yaml" select="ctrl.showEditor()" classes="btn-sm">
              <uib-tab-heading class="vertical-center" data-cy="k8sVolDetail-volYamlTab"> <pr-icon icon="'code'"></pr-icon> YAML </uib-tab-heading>
              <div class="px-5" ng-if="ctrl.state.showEditorTab">
                <kube-yaml-inspector data-cy="k8sVolDetail-volYaml" identifier="'volume-yaml'" data="ctrl.volume.PersistentVolumeClaim.Yaml" hide-message="true" />
              </div>
            </uib-tab>
          </uib-tabset>
        </rd-widget-body>
      </rd-widget>
    </div>
  </div>

  <kubernetes-integrated-applications-datatable
    ng-if="ctrl.isUsed(item)"
    dataset="ctrl.volume.Applications"
    is-loading="!ctrl.volume.Applications"
    table-key="'kubernetes.volume.applications'"
    on-refresh="(ctrl.getVolume)"
    table-title="'Applications using this volume'"
  >
  </kubernetes-integrated-applications-datatable>
</div>
