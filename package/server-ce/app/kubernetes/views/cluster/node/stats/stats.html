<page-header
  ng-if="ctrl.state.viewReady"
  title="'Node stats'"
  breadcrumbs="[
{ label:'Cluster', link:'kubernetes.cluster' },
{
  label:ctrl.state.transition.nodeName,
  link: 'kubernetes.cluster.node',
  linkParams:{nodeName: ctrl.state.transition.nodeName}
},
 ctrl.state.transition.nodeName,
 ]"
  reload="true"
></page-header>

<kubernetes-view-loading view-ready="ctrl.state.viewReady"></kubernetes-view-loading>

<information-panel ng-if="!ctrl.state.getMetrics" title-text="Unable to retrieve node metrics">
  <span class="small text-muted vertical-center">
    <pr-icon icon="'alert-triangle'" mode="'primary'"></pr-icon>
    <PERSON><PERSON><PERSON> was unable to retrieve any metrics associated to that node. Please contact your administrator to ensure that the Kubernetes metrics feature is properly configured.
  </span>
</information-panel>
<div class="row" ng-if="ctrl.state.getMetrics">
  <div class="col-md-12">
    <rd-widget>
      <div class="toolBar px-5 pt-5">
        <div class="toolBarTitle flex">
          <div class="widget-icon space-right">
            <pr-icon icon="'info'"></pr-icon>
          </div>
          <span class="vertical-center"> About statistics </span>
        </div>
      </div>
      <rd-widget-body>
        <form class="form-horizontal">
          <div class="form-group">
            <div class="col-sm-12">
              <span class="small text-muted">
                This view displays real-time statistics about the node <b>{{ ctrl.state.transition.nodeName }}</b
                >.
              </span>
            </div>
          </div>
          <div class="form-group">
            <label for="refreshRate" class="col-sm-3 col-md-2 col-lg-2 margin-sm-top control-label text-left"> Refresh rate </label>
            <div class="col-sm-3 col-md-2">
              <select id="refreshRate" ng-model="ctrl.state.refreshRate" ng-change="ctrl.changeUpdateRepeater()" class="form-control" data-cy="node-stats-refresh-rate">
                <option value="30">30s</option>
                <option value="60">60s</option>
              </select>
            </div>
            <span>
              <pr-icon id="refreshRateChange" icon="'check'" mode="'success'" style="display: none"></pr-icon>
            </span>
          </div>
        </form>
      </rd-widget-body>
    </rd-widget>
  </div>
</div>

<div class="row" ng-show="ctrl.state.getMetrics">
  <div class="col-lg-6 col-md-12 col-sm-12">
    <rd-widget>
      <div class="toolBar px-5 pt-5">
        <div class="toolBarTitle flex">
          <div class="widget-icon space-right">
            <pr-icon icon="'svg-memory'"></pr-icon>
          </div>
          <span class="vertical-center"> Memory usage </span>
        </div>
      </div>
      <rd-widget-body>
        <div class="chart-node" style="position: relative">
          <canvas id="memoryChart" width="770" height="300"></canvas>
        </div>
      </rd-widget-body>
    </rd-widget>
  </div>
  <div class="col-lg-6 col-md-12 col-sm-12">
    <rd-widget>
      <div class="toolBar px-5 pt-5">
        <div class="toolBarTitle flex">
          <div class="widget-icon space-right">
            <pr-icon icon="'cpu'"></pr-icon>
          </div>
          <span class="vertical-center"> CPU usage </span>
        </div>
      </div>
      <rd-widget-body>
        <div class="chart-node" style="position: relative">
          <canvas id="cpuChart" width="770" height="300"></canvas>
        </div>
      </rd-widget-body>
    </rd-widget>
  </div>
</div>
