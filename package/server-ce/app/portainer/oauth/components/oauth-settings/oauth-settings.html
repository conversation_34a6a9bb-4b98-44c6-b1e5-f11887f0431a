<ng-form name="oauthSettingsForm">
  <div class="col-sm-12 form-section-title"> Single Sign-On </div>

  <!-- SSO -->
  <div class="form-group">
    <div class="col-sm-12">
      <por-switch-field
        label="'Use SSO'"
        tooltip="'When using SSO the OAuth provider is not forced to prompt for credentials.'"
        name="'use-sso'"
        checked="$ctrl.settings.SSO"
        on-change="($ctrl.updateSSO)"
      ></por-switch-field>
    </div>
  </div>
  <!-- !SSO -->

  <!-- HideInternalAuth -->
  <div class="form-group" ng-if="$ctrl.settings.SSO">
    <div class="col-sm-12">
      <por-switch-field
        label="'Hide internal authentication prompt'"
        name="'hide-internal-auth'"
        feature-id="$ctrl.limitedFeature"
        checked="$ctrl.settings.HideInternalAuth"
        on-change="($ctrl.onChangeHideInternalAuth)"
      ></por-switch-field>
    </div>
  </div>
  <!-- !HideInternalAuth -->

  <auto-user-provision-toggle ng-model="$ctrl.settings.OAuthAutoCreateUsers">
    <field-description>
      With automatic user provisioning enabled, Portainer will create user(s) automatically with the standard user role. If disabled, users must be created beforehand in Portainer
      in order to login.
    </field-description>
  </auto-user-provision-toggle>

  <div ng-if="$ctrl.settings.OAuthAutoCreateUsers">
    <div class="form-group">
      <span class="col-sm-12 text-muted small">
        <p>The users created by the automatic provisioning feature can be added to a default team on creation.</p>
        <p>
          By assigning newly created users to a team, they will be able to access the environments associated to that team. This setting is optional and if not set, newly created
          users won't be able to access any environments.
        </p>
      </span>
    </div>
    <div class="form-group">
      <label class="col-sm-3 col-lg-2 control-label text-left">Default team</label>
      <span class="small text-muted" style="margin-left: 20px" ng-if="$ctrl.teams.length === 0">
        You have not yet created any teams. Head over to the <a ui-sref="portainer.teams">Teams view</a> to manage teams.
      </span>

      <div class="col-sm-9" ng-if="$ctrl.teams.length > 0">
        <div class="col-sm-12 small text-muted">
          <p class="vertical-center">
            <pr-icon icon="'info'" mode="'primary'"></pr-icon>
            The default team option will be disabled when automatic team membership is enabled
          </p>
        </div>
        <div class="col-xs-12 vertical-center">
          <select
            class="form-control"
            ng-disabled="$ctrl.settings.OAuthAutoMapTeamMemberships"
            ng-model="$ctrl.settings.DefaultTeamID"
            ng-options="team.Id as team.Name for team in $ctrl.teams"
            data-cy="default-team-select"
          >
            <option value="">No team</option>
          </select>
          <button
            type="button"
            class="btn btn-md btn-danger"
            ng-click="$ctrl.settings.DefaultTeamID = null"
            ng-disabled="!$ctrl.settings.DefaultTeamID || $ctrl.settings.OAuthAutoMapTeamMemberships"
            ng-if="$ctrl.teams.length > 0"
          >
            <pr-icon icon="'x'" size="'md'"></pr-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-12 form-section-title"> Team membership </div>
  <div class="form-group">
    <div class="col-sm-12 text-muted small"> Automatic team membership synchronizes the team membership based on a custom claim in the token from the OAuth provider. </div>
  </div>
  <div class="form-group">
    <div class="col-sm-12">
      <por-switch-field
        label="'Automatic team membership'"
        name="'tls'"
        feature-id="$ctrl.limitedFeature"
        checked="$ctrl.settings.OAuthAutoMapTeamMemberships"
        on-change="($ctrl.onToggleAutoTeamMembership)"
      ></por-switch-field>
    </div>
  </div>

  <div ng-if="$ctrl.settings.OAuthAutoMapTeamMemberships">
    <div class="form-group">
      <label class="col-sm-3 col-lg-2 control-label text-left">
        Claim name
        <portainer-tooltip message="'The OpenID Connect UserInfo Claim name that contains the team identifier the user belongs to.'"></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <div class="col-xs-11 col-lg-10">
          <input
            type="text"
            class="form-control"
            id="oauth_token_claim_name"
            ng-model="$ctrl.settings.TeamMemberships.OAuthClaimName"
            placeholder="groups"
            data-cy="oauth-token-claim-name"
          />
        </div>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-3 col-lg-2 control-label text-left"> Statically assigned teams </label>
      <div class="col-sm-9 col-lg-10">
        <span class="label label-default interactive vertical-center ml-4" ng-click="$ctrl.addTeamMembershipMapping()">
          <pr-icon icon="'plus-circle'"></pr-icon>
          add team mapping
        </span>

        <div class="col-sm-12 form-inline" ng-repeat="mapping in $ctrl.settings.TeamMemberships.OAuthClaimMappings" style="margin-top: 0.75em">
          <div class="input-group input-group-sm col-sm-5">
            <span class="input-group-addon">claim value regex</span>
            <input type="text" class="form-control" ng-model="mapping.ClaimValRegex" data-cy="claim-value-regex" />
          </div>
          <span style="margin: 0px 0.5em">maps to</span>
          <div class="input-group input-group-sm col-sm-3 col-lg-4">
            <span class="input-group-addon">team</span>
            <select
              class="form-control"
              data-cy="team-select"
              ng-init="mapping.Team = mapping.Team || $ctrl.settings.DefaultTeamID"
              ng-model="mapping.Team"
              ng-options="team.Id as team.Name for team in $ctrl.teams"
            >
              <option selected value="">Select a team</option>
            </select>
          </div>
          <button type="button" class="btn btn-md btn-danger" ng-click="$ctrl.removeTeamMembership($index)">
            <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
          </button>

          <div>
            <div class="small text-warning vertical-center mt-1" ng-show="!mapping.ClaimValRegex">
              <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
              Claim value regex is required.
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-group">
      <div class="col-sm-12 text-muted small" style="margin-bottom: 0.5em"> The default team will be assigned when the user does not belong to any other team </div>
      <label class="col-sm-3 col-lg-2 control-label text-left">Default team</label>
      <span class="small text-muted" style="margin-left: 20px" ng-if="$ctrl.teams.length === 0">
        You have not yet created any teams. Head over to the <a ui-sref="portainer.teams">Teams view</a> to manage teams.
      </span>

      <div class="col-sm-9" ng-if="$ctrl.teams.length > 0">
        <div class="col-xs-11">
          <select class="form-control" ng-model="$ctrl.settings.DefaultTeamID" ng-options="team.Id as team.Name for team in $ctrl.teams" data-cy="default-team-select">
            <option value="">No team</option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <oauth-providers-selector on-change="($ctrl.onSelectProvider)" value="$ctrl.state.provider"></oauth-providers-selector>

  <div ng-if="$ctrl.state.provider == 'custom' || $ctrl.state.overrideConfiguration">
    <div class="col-sm-12 form-section-title">OAuth Configuration</div>

    <div class="form-group">
      <label for="oauth_client_id" class="col-sm-3 col-lg-2 control-label text-left">
        {{ $ctrl.state.provider == 'microsoft' ? 'Application ID' : 'Client ID' }}
        <portainer-tooltip message="'Public identifier of the OAuth application'"></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          id="oauth_client_id"
          ng-model="$ctrl.settings.ClientID"
          placeholder="xxxxxxxxxxxxxxxxxxxx"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
          tabindex="{{ $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' ? -1 : 0 }}"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_client_secret" class="col-sm-3 col-lg-2 control-label text-left"> {{ $ctrl.state.provider == 'microsoft' ? 'Application key' : 'Client secret' }} </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="password"
          class="form-control"
          id="oauth_client_secret"
          ng-model="$ctrl.settings.ClientSecret"
          placeholder="xxxxxxxxxxxxxxxxxxxx"
          autocomplete="new-password"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
          tabindex="{{ $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' ? -1 : 0 }}"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_authorization_uri" class="col-sm-3 col-lg-2 control-label text-left">
        Authorization URL
        <portainer-tooltip message="'URL used to authenticate against the OAuth provider. Will redirect the user to the OAuth provider login view'"></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-authorization-uri"
          class="form-control"
          id="oauth_authorization_uri"
          ng-model="$ctrl.settings.AuthorizationURI"
          placeholder="https://example.com/oauth/authorize"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_access_token_uri" class="col-sm-3 col-lg-2 control-label text-left">
        Access token URL
        <portainer-tooltip message="'URL used by Portainer to exchange a valid OAuth authentication code for an access token'"></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-access-token-uri"
          class="form-control"
          id="oauth_access_token_uri"
          ng-model="$ctrl.settings.AccessTokenURI"
          placeholder="https://example.com/oauth/token"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_resource_uri" class="col-sm-3 col-lg-2 control-label text-left">
        Resource URL
        <portainer-tooltip message="'URL used by Portainer to retrieve information about the authenticated user'"></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-resource-uri"
          class="form-control"
          id="oauth_resource_uri"
          ng-model="$ctrl.settings.ResourceURI"
          placeholder="https://example.com/user"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_redirect_uri" class="col-sm-3 col-lg-2 control-label text-left">
        Redirect URL
        <portainer-tooltip
          message="'URL used by the OAuth provider to redirect the user after successful authentication. Should be set to your Portainer instance URL'"
        ></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-redirect-uri"
          class="form-control"
          id="oauth_redirect_uri"
          ng-model="$ctrl.settings.RedirectURI"
          placeholder="http://yourportainer.com/"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_logout_url" class="col-sm-3 col-lg-2 control-label text-left">
        Logout URL
        <portainer-tooltip
          message="'URL used by Portainer to redirect the user to the OAuth provider in order to log the user out of the identity provider session.'"
        ></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-logout-url"
          class="form-control"
          id="oauth_logout_url"
          ng-model="$ctrl.settings.LogoutURI"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_user_identifier" class="col-sm-3 col-lg-2 control-label text-left">
        User identifier
        <portainer-tooltip
          message="'Identifier that will be used by Portainer to create an account for the authenticated user. Retrieved from the resource server specified via the Resource URL field'"
        ></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-user-identifier"
          class="form-control"
          id="oauth_user_identifier"
          ng-model="$ctrl.settings.UserIdentifier"
          placeholder="id"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="oauth_scopes" class="col-sm-3 col-lg-2 control-label text-left">
        Scopes
        <portainer-tooltip
          message="'Scopes required by the OAuth provider to retrieve information about the authenticated user. Refer to your OAuth provider documentation for more information about this'"
        ></portainer-tooltip>
      </label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          data-cy="oauth-scopes"
          class="form-control"
          id="oauth_scopes"
          ng-model="$ctrl.settings.Scopes"
          placeholder="id,email,name"
          ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
          ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
        />
      </div>
    </div>

    <oauth-auth-style value="$ctrl.settings.AuthStyle" on-change="($ctrl.onChangeAuthStyle)"></oauth-auth-style>
    <save-auth-settings-button
      on-save-settings="($ctrl.onSaveSettings)"
      save-button-state="($ctrl.saveButtonState)"
      save-button-disabled="!$ctrl.isOAuthTeamMembershipFormValid() || oauthSettingsForm.$invalid"
      limited-feature-id="$ctrl.limitedFeature"
      limited-feature-class="$ctrl.limitedFeatureClass"
      class-name="'oauth-save-settings-button'"
    ></save-auth-settings-button>
  </div>

  <div ng-if="$ctrl.state.provider != 'custom'" class="limited-be be-indicator-container">
    <div class="limited-be-link vertical-center"><be-feature-indicator feature="$ctrl.limitedFeature"></be-feature-indicator></div>
    <div class="limited-be-content">
      <div class="col-sm-12 form-section-title">OAuth Configuration</div>

      <div class="form-group" ng-if="$ctrl.state.provider == 'microsoft'">
        <label for="oauth_microsoft_tenant_id" class="col-sm-3 col-lg-2 control-label text-left">
          Tenant ID
          <portainer-tooltip message="'ID of the Azure Directory you wish to authenticate against. Also known as the Directory ID'"></portainer-tooltip>
        </label>
        <div class="col-sm-9 col-lg-10">
          <input
            type="text"
            data-cy="oauth-microsoft-tenant-id"
            class="form-control"
            id="oauth_microsoft_tenant_id"
            placeholder="xxxxxxxxxxxxxxxxxxxx"
            ng-model="$ctrl.state.microsoftTenantID"
            ng-change="$ctrl.onMicrosoftTenantIDChange()"
            limited-feature-dir="{{::$ctrl.limitedFeature}}"
            limited-feature-class="limited-be"
            limited-feature-disabled
            limited-feature-tabindex="-1"
            required
          />
        </div>
      </div>

      <div class="form-group">
        <label for="oauth_client_id" class="col-sm-3 col-lg-2 control-label text-left">
          {{ $ctrl.state.provider == 'microsoft' ? 'Application ID' : 'Client ID' }}
          <portainer-tooltip message="'Public identifier of the OAuth application'"></portainer-tooltip>
        </label>
        <div class="col-sm-9 col-lg-10">
          <input
            type="text"
            data-cy="oauth-client-id"
            id="oauth_client_id"
            ng-model="$ctrl.settings.ClientID"
            placeholder="xxxxxxxxxxxxxxxxxxxx"
            ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
            ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
            tabindex="{{ $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' ? -1 : 0 }}"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="oauth_client_secret" class="col-sm-3 col-lg-2 control-label text-left"> {{ $ctrl.state.provider == 'microsoft' ? 'Application key' : 'Client secret' }} </label>
        <div class="col-sm-9 col-lg-10">
          <input
            type="password"
            class="form-control"
            id="oauth_client_secret"
            ng-model="$ctrl.settings.ClientSecret"
            placeholder="*******"
            autocomplete="new-password"
            ng-class="['form-control', { 'limited-be': $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' }]"
            ng-disabled="$ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom'"
            tabindex="{{ $ctrl.isLimitedToBE && $ctrl.state.provider !== 'custom' ? -1 : 0 }}"
          />
        </div>
      </div>

      <div class="form-group">
        <div class="col-sm-12">
          <a class="small interactive vertical-center" ng-if="!$ctrl.state.overrideConfiguration" ng-click="$ctrl.state.overrideConfiguration = true;">
            <pr-icon icon="'wrench'"></pr-icon>
            Override default configuration
          </a>
          <a class="small interactive vertical-center" ng-if="$ctrl.state.overrideConfiguration" ng-click="$ctrl.useDefaultProviderConfiguration($ctrl.state.provider)">
            <pr-icon icon="'settings'"></pr-icon>
            Use default configuration
          </a>
        </div>
      </div>
      <save-auth-settings-button
        on-save-settings="($ctrl.onSaveSettings)"
        save-button-state="($ctrl.saveButtonState)"
        save-button-disabled="!$ctrl.isOAuthTeamMembershipFormValid() || oauthSettingsForm.$invalid"
        limited-feature-id="$ctrl.limitedFeature"
        limited-feature-class="$ctrl.limitedFeatureClass"
        class-name="'oauth-save-settings-button'"
      ></save-auth-settings-button>
    </div>
  </div>
</ng-form>
