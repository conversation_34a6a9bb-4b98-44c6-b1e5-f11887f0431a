<div class="col-sm-12 form-section-title" style="float: initial"> Group search configurations </div>

<rd-widget ng-repeat="config in $ctrl.settings | limitTo: (1 - $ctrl.settings)" style="display: block; margin-bottom: 10px">
  <rd-widget-body>
    <div class="form-group" ng-if="$index > 0" style="margin-bottom: 10px">
      <span class="col-sm-12 text-muted small"> Extra search configuration </span>
    </div>

    <div class="form-group">
      <label for="ldap_group_basedn_{{ $index }}" class="col-sm-4 col-md-2 control-label text-left">
        Group Base DN
        <portainer-tooltip message="'The distinguished name of the element from which the LDAP server will search for groups.'"></portainer-tooltip>
      </label>
      <div class="col-sm-8 col-md-4">
        <input
          type="text"
          class="form-control"
          id="ldap_group_basedn_{{ $index }}"
          ng-model="config.GroupBaseDN"
          placeholder="dc=ldap,dc=domain,dc=tld"
          data-cy="ldap-group-basedn-input"
        />
      </div>

      <label for="ldap_group_att_{{ $index }}" class="col-sm-4 col-md-2 control-label text-left">
        Group Membership Attribute
        <portainer-tooltip message="'LDAP attribute which denotes the group membership.'"></portainer-tooltip>
      </label>
      <div class="col-sm-8 col-md-4">
        <input type="text" class="form-control" id="ldap_group_att_{{ $index }}" ng-model="config.GroupAttribute" placeholder="member" data-cy="ldap-group-attribute-input" />
      </div>
    </div>
    <div class="form-group">
      <label for="ldap_group_filter_{{ $index }}" class="col-sm-4 col-md-2 control-label text-left">
        Group Filter
        <portainer-tooltip message="'The LDAP search filter used to select group elements, optional.'"></portainer-tooltip>
      </label>
      <div class="col-sm-8 col-md-10 vertical-center">
        <input
          type="text"
          class="form-control"
          id="ldap_group_filter_{{ $index }}"
          ng-model="config.GroupFilter"
          placeholder="(objectClass=account)"
          data-cy="ldap-group-filter-input"
        />
        <button class="btn btn-md btn-danger" type="button" ng-click="$ctrl.onRemoveClick($index)" ng-if="$index > 0">
          <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
        </button>
      </div>
    </div>
    <div class="form-group">
      <span class="col-sm-12 small" style="color: #ffa719">
        <pr-icon icon="'briefcase'" class-name="'icon icon-xs vertical-center'"></pr-icon>
        Users removal synchronize between groups and teams only available in
        <a href="https://www.portainer.io/features?from=custom-login-banner" target="_blank">business edition.</a>
        <portainer-tooltip
          class="text-muted align-bottom"
          message="'Groups allows users to automatically be added to Portainer teams. However, automatically removing users from teams to keep it fully in sync is available in the Business Edition.'"
        ></portainer-tooltip>
      </span>
    </div>
  </rd-widget-body>
</rd-widget>

<div class="form-group" style="margin-top: 10px">
  <div class="col-sm-12">
    <button class="btn btn-sm btn-light vertical-center !ml-0" ng-click="$ctrl.onAddClick()">
      <pr-icon icon="'plus'"></pr-icon>
      Add group search configuration
    </button>
  </div>
  <div class="col-sm-12" style="margin-top: 10px">
    <be-teaser-button
      feature-id="$ctrl.limitedFeatureId"
      heading="'Display User/Group matching'"
      message="'Show the list of users and groups that match the Portainer search configurations.'"
      button-text="'Display User/Group matching'"
      button-class-name="'!ml-0'"
    ></be-teaser-button>
  </div>
</div>

<ldap-groups-datatable ng-if="$ctrl.showTable" dataset="$ctrl.groups"></ldap-groups-datatable>
