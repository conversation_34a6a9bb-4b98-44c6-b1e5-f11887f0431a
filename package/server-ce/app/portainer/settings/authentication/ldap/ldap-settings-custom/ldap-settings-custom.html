<div>
  <div class="col-sm-12 form-section-title"> Information </div>
  <div class="form-group col-sm-12 text-muted small">
    When using LDAP authentication, <PERSON><PERSON><PERSON> will delegate user authentication to a LDAP server and fallback to internal authentication if LDAP authentication fails.
  </div>
</div>

<div class="col-sm-12 form-section-title"> LDAP configuration </div>

<div class="form-group">
  <div class="col-sm-12 small text-muted">
    <p class="vertical-center">
      <pr-icon icon="'info'" mode="'primary'"></pr-icon>
      You can configure multiple LDAP Servers for authentication fallback. Make sure all servers are using the same configuration (i.e. if TLS is enabled, they should all use the
      same certificates).
    </p>
  </div>
</div>

<div class="form-group">
  <label for="ldap_url" class="col-sm-3 col-lg-2 control-label flex flex-wrap text-left"> LDAP Server </label>
  <div class="col-sm-9 col-lg-10">
    <div class="mb-3 flex" ng-repeat="url in $ctrl.settings.URLs track by $index">
      <input
        type="text"
        class="form-control"
        id="ldap_url"
        ng-model="$ctrl.settings.URLs[$index]"
        placeholder="e.g. *********:389 or myldap.domain.tld:389"
        required
        data-cy="ldap-url-input"
      />
      <button ng-if="$index > 0" class="btn btn-sm btn-danger" type="button" ng-click="$ctrl.removeLDAPUrl($index)">
        <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
      </button>
    </div>
  </div>
  <div class="col-sm-12">
    <be-teaser-button
      feature-id="$ctrl.limitedFeatureId"
      heading="'Add additional server'"
      message="'Allows you to add an additional LDAP server.'"
      button-text="'Add additional server'"
      button-class-name="'!ml-0'"
    ></be-teaser-button>
  </div>
</div>

<div class="form-group">
  <label for="anonymous_mode" class="control-label col-sm-3 col-lg-2 text-left">
    Anonymous mode
    <portainer-tooltip message="'Enable this option if the server is configured for Anonymous access.'"></portainer-tooltip>
  </label>
  <div class="col-sm-9 col-lg-10">
    <label class="switch">
      <input
        type="checkbox"
        id="anonymous_mode"
        ng-model="$ctrl.settings.AnonymousMode"
        limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
        limited-feature-tabindex="-1"
        data-cy="anonymous-mode-checkbox"
      />
      <span class="slider round"></span>
    </label>
  </div>
</div>
<!-- !Anonymous mode-->

<div ng-if="!$ctrl.settings.AnonymousMode">
  <div class="form-group">
    <label for="ldap_username" class="col-sm-3 col-lg-2 control-label text-left">
      Reader DN
      <portainer-tooltip message="'Account that will be used to search for users.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" id="ldap_username" ng-model="$ctrl.settings.ReaderDN" placeholder="cn=user,dc=domain,dc=tld" data-cy="ldap-username-input" />
    </div>
  </div>

  <div class="form-group">
    <label for="ldap_password" class="col-sm-3 col-lg-2 control-label text-left">
      Password
      <portainer-tooltip message="'If you do not enter a password, Portainer will leave the current password unchanged.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input type="password" class="form-control" id="ldap_password" ng-model="$ctrl.settings.Password" placeholder="password" autocomplete="new-password" />
    </div>
  </div>
</div>

<ldap-connectivity-check
  ng-if="!$ctrl.settings.TLSConfig.TLS && !$ctrl.settings.StartTLS"
  settings="$ctrl.settings"
  state="$ctrl.state"
  connectivity-check="$ctrl.connectivityCheck"
></ldap-connectivity-check>

<ldap-settings-security
  settings="$ctrl.settings"
  tlsca-cert="$ctrl.tlscaCert"
  upload-in-progress="$ctrl.state.uploadInProgress"
  on-tlsca-cert-change="($ctrl.onTlscaCertChange)"
></ldap-settings-security>

<ldap-connectivity-check
  ng-if="$ctrl.settings.TLSConfig.TLS || $ctrl.settings.StartTLS"
  settings="$ctrl.settings"
  state="$ctrl.state"
  connectivity-check="$ctrl.connectivityCheck"
></ldap-connectivity-check>

<div class="space-y-10">
  <ldap-custom-user-search
    class="block"
    settings="$ctrl.settings.SearchSettings"
    on-search-click="($ctrl.onSearchUsersClick)"
    limited-feature-id="$ctrl.limitedFeatureId"
  ></ldap-custom-user-search>

  <ldap-custom-group-search
    class="block"
    settings="$ctrl.settings.GroupSearchSettings"
    on-search-click="($ctrl.onSearchGroupsClick)"
    limited-feature-id="$ctrl.limitedFeatureId"
  ></ldap-custom-group-search>

  <ldap-custom-admin-group
    class="block"
    settings="$ctrl.settings"
    on-search-click="($ctrl.onSearchAdminGroupsClick)"
    selected-admin-groups="$ctrl.selectedAdminGroups"
    default-admin-group-search-filter="'(objectClass=groupOfNames)'"
    limited-feature-id="$ctrl.limitedFeatureId"
    is-limited-feature-self-contained="true"
  ></ldap-custom-admin-group>

  <ldap-settings-test-login
    class="block"
    settings="$ctrl.settings"
    limited-feature-id="$ctrl.limitedFeatureId"
    show-be-indicator-if-needed="true"
    is-limited-feature-self-contained="true"
  ></ldap-settings-test-login>
</div>

<save-auth-settings-button
  on-save-settings="($ctrl.onSaveSettings)"
  save-button-state="($ctrl.saveButtonState)"
  save-button-disabled="!$ctrl.saveButtonDisabled()"
  limited-feature-dir="{{ $ctrl.limitedFeatureId }}"
></save-auth-settings-button>
