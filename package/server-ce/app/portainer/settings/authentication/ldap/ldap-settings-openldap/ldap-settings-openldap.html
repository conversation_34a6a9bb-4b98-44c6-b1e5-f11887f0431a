<ng-form limited-feature-dir="{{::$ctrl.limitedFeatureId}}" limited-feature-class="limited-be" class="ldap-settings-openldap">
  <div class="be-indicator-container">
    <div class="limited-be-link vertical-center"><be-feature-indicator feature="$ctrl.limitedFeatureId"></be-feature-indicator></div>
    <div class="limited-be-content">
      <div>
        <div class="col-sm-12 form-section-title"> Information </div>
        <div class="form-group col-sm-12 text-muted small">
          When using LDAP authentication, <PERSON><PERSON><PERSON> will delegate user authentication to a LDAP server and fallback to internal authentication if LDAP authentication fails.
        </div>
      </div>

      <div class="col-sm-12 form-section-title"> LDAP configuration </div>

      <div class="form-group">
        <div class="col-sm-12 small text-muted">
          <p class="vertical-center">
            <pr-icon icon="'info'" mode="'primary'"></pr-icon>
            You can configure multiple LDAP Servers for authentication fallback. Make sure all servers are using the same configuration (i.e. if TLS is enabled, they should all use
            the same certificates).
          </p>
        </div>
      </div>

      <div class="form-group">
        <label for="ldap_url" class="col-sm-3 col-lg-2 control-label text-left" style="display: flex; flex-wrap: wrap">
          LDAP Server
          <button
            type="button"
            class="label label-default interactive vertical-center"
            style="border: 0"
            ng-click="$ctrl.addLDAPUrl()"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-tabindex="-1"
          >
            <pr-icon icon="'plus-circle'"></pr-icon>
            Add additional server
          </button>
        </label>
        <div class="col-sm-9 col-lg-10">
          <div ng-repeat="url in $ctrl.settings.URLs track by $index" style="display: flex; margin-bottom: 10px">
            <input
              type="text"
              data-cy="ldap-url"
              class="form-control"
              id="ldap_url"
              ng-model="$ctrl.settings.URLs[$index]"
              placeholder="e.g. 10.0.0.10:389 or myldap.domain.tld:389"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            />
            <button
              ng-if="$index > 0"
              class="btn btn-sm btn-danger"
              type="button"
              ng-click="$ctrl.removeLDAPUrl($index)"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            >
              <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- Anonymous mode-->
      <div class="form-group">
        <div class="col-sm-12">
          <label for="anonymous_mode" class="control-label col-sm-3 col-lg-2 text-left">
            Anonymous mode
            <portainer-tooltip message="'Enable this option if the server is configured for Anonymous access.'"></portainer-tooltip>
          </label>

          <div class="col-sm-9 col-lg-10">
            <label class="switch">
              <input
                type="checkbox"
                id="anonymous_mode"
                ng-model="$ctrl.settings.AnonymousMode"
                limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
                limited-feature-tabindex="-1"
                data-cy="ldap-anonymous-mode"
              />
              <span class="slider round"></span>
            </label>
          </div>
        </div>
      </div>
      <!-- !Anonymous mode-->

      <div ng-if="!$ctrl.settings.AnonymousMode">
        <div class="form-group">
          <label for="ldap_username" class="col-sm-3 col-lg-2 control-label text-left">
            Reader DN
            <portainer-tooltip message="'Account that will be used to search for users.'"></portainer-tooltip>
          </label>
          <div class="col-sm-9 col-lg-10">
            <input
              type="text"
              data-cy="ldap-reader-dn"
              class="form-control"
              id="ldap_username"
              ng-model="$ctrl.settings.ReaderDN"
              placeholder="cn=user,dc=domain,dc=tld"
              ng-change="$ctrl.onAccountChange($ctrl.settings.ReaderDN)"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="ldap_password" class="col-sm-3 col-lg-2 control-label text-left">
            Password
            <portainer-tooltip message="'If you do not enter a password, Portainer will leave the current password unchanged.'"></portainer-tooltip>
          </label>
          <div class="col-sm-9">
            <input
              type="password"
              class="form-control"
              id="ldap_password"
              ng-model="$ctrl.settings.Password"
              placeholder="password"
              autocomplete="new-password"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            />
          </div>
        </div>
      </div>

      <div class="form-group" ng-if="$ctrl.settings.AnonymousMode">
        <label for="ldap_domain_root" class="col-sm-3 col-lg-2 control-label text-left"> Domain root </label>
        <div class="col-sm-9">
          <input
            type="text"
            data-cy="ldap-domain-root"
            class="form-control"
            id="ldap_domain_root"
            ng-model="$ctrl.domainSuffix"
            placeholder="dc=domain,dc=tld"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-tabindex="-1"
          />
        </div>
      </div>

      <ldap-connectivity-check
        ng-if="!$ctrl.settings.TLSConfig.TLS && !$ctrl.settings.StartTLS"
        settings="$ctrl.settings"
        state="$ctrl.state"
        connectivity-check="$ctrl.connectivityCheck"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-connectivity-check>

      <ldap-settings-security
        title="Connectivity Security"
        settings="$ctrl.settings"
        tlsca-cert="$ctrl.tlscaCert"
        upload-in-progress="$ctrl.state.uploadInProgress"
        on-tlsca-cert-change="($ctrl.onTlscaCertChange)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-settings-security>

      <ldap-connectivity-check
        ng-if="$ctrl.settings.TLSConfig.TLS || $ctrl.settings.StartTLS"
        settings="$ctrl.settings"
        state="$ctrl.state"
        connectivity-check="$ctrl.connectivityCheck"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-connectivity-check>

      <ldap-user-search
        style="margin-top: 5px"
        settings="$ctrl.settings.SearchSettings"
        domain-suffix="{{ $ctrl.domainSuffix }}"
        base-filter="(objectClass=inetOrgPerson)"
        on-search-click="($ctrl.onSearchUsersClick)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-user-search>

      <ldap-group-search
        style="margin-top: 5px"
        settings="$ctrl.settings.GroupSearchSettings"
        domain-suffix="{{ $ctrl.domainSuffix }}"
        base-filter="(objectClass=groupOfNames)"
        on-search-click="($ctrl.onSearchGroupsClick)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-group-search>

      <ldap-settings-test-login settings="$ctrl.settings" limited-feature-id="$ctrl.limitedFeatureId"></ldap-settings-test-login>
      <save-auth-settings-button
        on-save-settings="($ctrl.onSaveSettings)"
        save-button-state="($ctrl.saveButtonState)"
        save-button-disabled="!$ctrl.saveButtonDisabled()"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></save-auth-settings-button>
    </div>
  </div>
</ng-form>
