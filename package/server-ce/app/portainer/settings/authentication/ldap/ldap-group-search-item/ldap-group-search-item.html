<div class="w-full px-5 pt-3">
  <div ng-if="$ctrl.index > 0" style="margin-bottom: 10px">
    <span class="text-muted small"> Extra search configuration </span>
    <button
      class="btn btn-sm btn-danger"
      type="button"
      ng-click="$ctrl.onRemoveClick($ctrl.index)"
      limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
      limited-feature-tabindex="-1"
    >
      <pr-icon icon="'trash-2'"></pr-icon>
    </button>
  </div>

  <ldap-settings-dn-builder
    label="Group Search Path (optional)"
    suffix="{{ $ctrl.domainSuffix }}"
    ng-model="$ctrl.config.GroupBaseDN"
    on-change="($ctrl.onChangeBaseDN)"
    limited-feature-id="$ctrl.limitedFeatureId"
  ></ldap-settings-dn-builder>

  <div class="form-group">
    <label class="col-sm-4 col-md-2 control-label text-left"> Group Base DN </label>
    <div class="col-sm-8 col-md-10"> {{ $ctrl.config.GroupBaseDN }} </div>
  </div>

  <div class="form-group">
    <div class="col-sm-12 vertical-center" style="margin-bottom: 5px">
      <label class="control-label !pt-0 text-left">Groups</label>
      <span class="label label-default interactive vertical-center" style="margin-left: 10px" ng-click="$ctrl.addGroup()">
        <pr-icon icon="'plus-circle'"></pr-icon>
        add another group
      </span>
    </div>
    <div class="col-sm-12" ng-if="$ctrl.groups.length">
      <div class="w-full px-5 pt-3">
        <div class="form-group no-margin-last-child" ng-repeat="entry in $ctrl.groups">
          <div class="col-sm-4">
            <select
              class="form-control"
              data-cy="ldap-group-search-item-select"
              ng-model="entry.type"
              ng-change="$ctrl.onGroupsChange()"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            >
              <option value="ou">OU Name</option>
              <option value="cn">Folder Name</option>
            </select>
          </div>
          <div class="col-sm-5">
            <input
              class="form-control"
              ng-model="entry.value"
              ng-change="$ctrl.onGroupsChange()"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            />
          </div>
          <div class="col-sm-3 text-right">
            <button
              class="btn btn-md btn-danger"
              type="button"
              ng-click="$ctrl.removeGroup($index)"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            >
              <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="form-group no-margin-last-child">
    <label class="col-sm-4 col-md-2 control-label text-left"> Group Filter </label>
    <div class="col-sm-8 col-md-10"> {{ $ctrl.config.GroupFilter }} </div>
  </div>
</div>
