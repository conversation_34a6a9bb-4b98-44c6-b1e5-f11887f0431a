<div class="col-sm-12 form-section-title flex items-center" style="float: initial">
  Test login
  <be-feature-indicator
    ng-if="$ctrl.showBeIndicatorIfNeeded"
    feature="$ctrl.limitedFeatureId"
    class="space-left"
    ng-if="$ctrl.isLimitedFeatureSelfContained"
  ></be-feature-indicator>
</div>

<rd-widget>
  <rd-widget-body>
    <div class="form-inline">
      <div class="form-group" style="margin: 0">
        <label for="ldap_test_username" style="font-size: 0.9em; margin-right: 5px"> Username </label>
        <input
          type="text"
          class="form-control"
          id="ldap_test_username"
          ng-model="$ctrl.username"
          limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
          limited-feature-class=" {{ $ctrl.isLimitedFeatureSelfContained && 'limited-be' }}"
          ng-disabled="{{ $ctrl.isLimitedFeatureSelfContained }}"
          limited-feature-tabindex="-1"
          data-cy="ldap-test-username"
        />
      </div>

      <div class="form-group no-margin">
        <label for="ldap_test_password"> Password </label>
        <input
          type="password"
          class="form-control"
          id="ldap_test_password"
          ng-model="$ctrl.password"
          autocomplete="new-password"
          limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
          limited-feature-class=" {{ $ctrl.isLimitedFeatureSelfContained && 'limited-be' }}"
          ng-disabled="{{ $ctrl.isLimitedFeatureSelfContained }}"
          limited-feature-tabindex="-1"
        />
      </div>

      <div class="form-group !ml-0">
        <div class="vertical-center">
          <button
            type="submit"
            class="btn btn-primary"
            ng-disabled="$ctrl.state.testStatus === $ctrl.TEST_STATUS.LOADING || !$ctrl.username || !$ctrl.password"
            ng-click="$ctrl.testLogin($ctrl.username, $ctrl.password)"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-class=" {{ $ctrl.isLimitedFeatureSelfContained && 'limited-be' }}"
            limited-feature-tabindex="-1"
          >
            <span ng-if="$ctrl.state.testStatus !== $ctrl.TEST_STATUS.LOADING">Test</span>
            <span ng-if="$ctrl.state.testStatus === $ctrl.TEST_STATUS.LOADING">Testing...</span>
          </button>
          <pr-icon icon="'check'" class="icon-success" ng-if="$ctrl.state.testStatus === $ctrl.TEST_STATUS.SUCCESS"></pr-icon>
          <pr-icon icon="'x'" class="icon-danger" ng-if="$ctrl.state.testStatus === $ctrl.TEST_STATUS.FAILURE"></pr-icon>
        </div>
      </div>
    </div>
  </rd-widget-body>
</rd-widget>
