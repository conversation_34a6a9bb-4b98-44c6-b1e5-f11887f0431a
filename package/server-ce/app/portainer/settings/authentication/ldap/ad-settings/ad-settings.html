<ng-form class="ad-settings" limited-feature-dir="{{::$ctrl.limitedFeatureId}}" limited-feature-class="limited-be">
  <div class="be-indicator-container">
    <div class="limited-be-link vertical-center"><be-feature-indicator feature="$ctrl.limitedFeatureId"></be-feature-indicator></div>
    <div class="limited-be-content">
      <auto-user-provision-toggle ng-model="$ctrl.settings.AutoCreateUsers">
        <field-description>
          With automatic user provisioning enabled, <PERSON><PERSON><PERSON> will create user(s) automatically with standard user role and assign them to team(s) which matches to LDAP group
          name(s). If disabled, users must be created in <PERSON><PERSON><PERSON> beforehand.
        </field-description>
      </auto-user-provision-toggle>

      <div>
        <div class="col-sm-12 form-section-title"> Information </div>
        <div class="form-group col-sm-12 text-muted small">
          When using Microsoft AD authentication, <PERSON><PERSON><PERSON> will delegate user authentication to the Domain Controller(s) configured below; if there is no connectivity, <PERSON><PERSON>r
          will fallback to internal authentication.
        </div>
      </div>

      <div class="col-sm-12 form-section-title"> AD configuration </div>

      <div class="form-group">
        <div class="col-sm-12 small text-muted">
          <p class="vertical-center">
            <pr-icon icon="'info'" mode="'primary'"></pr-icon>
            You can configure multiple AD Controllers for authentication fallback. Make sure all servers are using the same configuration (i.e. if TLS is enabled, they should all
            use the same certificates).
          </p>
        </div>
      </div>

      <div class="form-group">
        <label for="ldap_url" class="col-sm-3 col-lg-2 control-label text-left" style="display: flex; flex-wrap: wrap">
          AD Controller
          <button
            type="button"
            class="label label-default interactive vertical-center"
            style="border: 0"
            ng-click="$ctrl.addLDAPUrl()"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-tabindex="-1"
          >
            <pr-icon icon="'plus-circle'"></pr-icon>
            Add additional server
          </button>
        </label>
        <div class="col-sm-9 col-lg-10">
          <div ng-repeat="url in $ctrl.settings.URLs track by $index" style="display: flex; margin-bottom: 10px">
            <input
              type="text"
              data-cy="ldap-url"
              class="form-control"
              id="ldap_url"
              ng-model="$ctrl.settings.URLs[$index]"
              placeholder="e.g. 10.0.0.10:389 or myldap.domain.tld:389"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            />
            <button
              ng-if="$index > 0"
              class="btn btn-sm btn-danger"
              type="button"
              ng-click="$ctrl.removeLDAPUrl($index)"
              limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
              limited-feature-tabindex="-1"
            >
              <pr-icon icon="'trash-2'"></pr-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="ldap_username" class="col-sm-3 control-label text-left">
          Service Account
          <portainer-tooltip message="'Account that will be used to search for users.'"></portainer-tooltip>
        </label>
        <div class="col-sm-9">
          <input
            type="text"
            data-cy="ldap-username"
            class="form-control"
            id="ldap_username"
            ng-model="$ctrl.settings.ReaderDN"
            placeholder="<EMAIL>"
            ng-change="$ctrl.onAccountChange($ctrl.settings.ReaderDN)"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-tabindex="-1"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="ldap_password" class="col-sm-3 control-label text-left">
          Service Account Password
          <portainer-tooltip message="'If you do not enter a password, Portainer will leave the current password unchanged.'"></portainer-tooltip>
        </label>
        <div class="col-sm-9">
          <input
            type="password"
            class="form-control"
            id="ldap_password"
            ng-model="$ctrl.settings.Password"
            placeholder="password"
            autocomplete="new-password"
            limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
            limited-feature-tabindex="-1"
          />
        </div>
      </div>

      <ldap-connectivity-check
        ng-if="!$ctrl.settings.TLSConfig.TLS && !$ctrl.settings.StartTLS"
        settings="$ctrl.settings"
        state="$ctrl.state"
        connectivity-check="$ctrl.connectivityCheck"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-connectivity-check>

      <ldap-settings-security
        title="AD Connectivity Security"
        settings="$ctrl.settings"
        tlsca-cert="$ctrl.tlscaCert"
        upload-in-progress="$ctrl.state.uploadInProgress"
        on-tlsca-cert-change="($ctrl.onTlscaCertChange)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-settings-security>

      <ldap-connectivity-check
        ng-if="$ctrl.settings.TLSConfig.TLS || $ctrl.settings.StartTLS"
        settings="$ctrl.settings"
        state="$ctrl.state"
        connectivity-check="$ctrl.connectivityCheck"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-connectivity-check>

      <ldap-user-search
        style="margin-top: 5px"
        show-username-format="true"
        settings="$ctrl.settings.SearchSettings"
        domain-suffix="{{ $ctrl.domainSuffix }}"
        base-filter="(objectClass=user)"
        on-search-click="($ctrl.searchUsers)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-user-search>

      <ldap-group-search
        style="margin-top: 5px"
        settings="$ctrl.settings.GroupSearchSettings"
        domain-suffix="{{ $ctrl.domainSuffix }}"
        base-filter="(objectClass=group)"
        on-search-click="($ctrl.searchGroups)"
        limited-feature-id="$ctrl.limitedFeatureId"
      ></ldap-group-search>

      <ldap-custom-admin-group
        style="margin-top: 5px"
        settings="$ctrl.settings"
        on-search-click="($ctrl.onSearchAdminGroupsClick)"
        selected-admin-groups="$ctrl.selectedAdminGroups"
        default-admin-group-search-filter="'(objectClass=groupOfNames)'"
        limited-feature-id="$ctrl.limitedFeatureId"
        is-limited-feature-self-contained="false"
      ></ldap-custom-admin-group>

      <ldap-settings-test-login settings="$ctrl.settings" limited-feature-id="$ctrl.limitedFeatureId" is-limited-feature-self-contained="false"></ldap-settings-test-login>
      <save-auth-settings-button
        on-save-settings="($ctrl.onSaveSettings)"
        save-button-state="($ctrl.saveButtonState)"
        limited-feature-id="$ctrl.limitedFeatureId"
        save-button-disabled="($ctrl.isSaveSettingButtonDisabled())"
      ></save-auth-settings-button>
    </div>
  </div>
</ng-form>
