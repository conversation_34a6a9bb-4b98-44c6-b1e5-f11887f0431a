<div class="form-group">
  <label for="group-name-input" class="col-sm-4 control-label text-left"> Group Name </label>
  <div class="col-sm-7" style="padding-left: 0">
    <input
      type="text"
      data-cy="group-name-input"
      class="form-control"
      id="group-name-input"
      ng-model="$ctrl.groupName"
      ng-change="$ctrl.onGroupNameChange()"
      limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
      limited-feature-tabindex="-1"
    />
  </div>
  <div class="col-sm-1">
    <button
      type="button"
      class="btn btn-danger btn-md vertical-center"
      ng-if="$ctrl.onRemoveClick"
      ng-click="$ctrl.onRemoveClick()"
      limited-feature-dir="{{::$ctrl.limitedFeatureId}}"
      limited-feature-tabindex="-1"
    >
      <pr-icon icon="'trash-2'" size="'md'"></pr-icon>
    </button>
  </div>
</div>

<ldap-settings-dn-builder
  ng-model="$ctrl.entries"
  label="Path to group"
  suffix="{{ $ctrl.suffix }}"
  on-change="($ctrl.onEntriesChange)"
  on-remove-click="($ctrl.removeGroup)"
  limited-feature-id="$ctrl.limitedFeatureId"
></ldap-settings-dn-builder>
