<form class="form-horizontal" name="$ctrl.registryFormAzure" ng-submit="$ctrl.formAction()">
  <div class="col-sm-12 form-section-title"> Azure registry details </div>
  <!-- name-input -->
  <div class="form-group">
    <label for="registry_name" class="col-sm-3 col-lg-2 control-label required text-left">Name</label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_name"
        name="registry_name"
        ng-model="$ctrl.model.Name"
        placeholder="my-azure-registry"
        required
        auto-focus
        data-cy="registry-name-input"
      />
      <div class="help-block" ng-show="$ctrl.registryFormAzure.registry_name.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormAzure.registry_name.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
            <p ng-message="used" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              A registry with the same name already exists.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !name-input -->
  <!-- url-input -->
  <div class="form-group">
    <label for="registry_url" class="col-sm-3 col-lg-2 control-label text-left">
      <span class="required">Registry URL</span>
      <portainer-tooltip message="'URL of an Azure Container Registry. Any protocol will be stripped.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_url"
        name="registry_url"
        ng-model="$ctrl.model.URL"
        placeholder="myproject.azurecr.io"
        required
        data-cy="registry-url-input"
      />
      <div class="help-block" ng-show="$ctrl.registryFormAzure.registry_url.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormAzure.registry_url.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- url-input -->
  <!-- credentials-user -->
  <div class="form-group">
    <label for="registry_username" class="col-sm-3 col-lg-2 control-label required text-left">Username</label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" id="registry_username" name="registry_username" ng-model="$ctrl.model.Username" required data-cy="registry-username-input" />
      <div class="help-block" ng-show="$ctrl.registryFormAzure.registry_username.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormAzure.registry_username.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-user -->
  <!-- credentials-password -->
  <div class="form-group">
    <label for="registry_password" class="col-sm-3 col-lg-2 control-label required text-left">Password</label>
    <div class="col-sm-9 col-lg-10">
      <input type="password" class="form-control" id="registry_password" name="registry_password" ng-model="$ctrl.model.Password" required />
      <div class="help-block" ng-show="$ctrl.registryFormAzure.registry_password.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormAzure.registry_password.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-password -->
  <!-- actions -->
  <div class="col-sm-12 form-section-title"> Actions </div>
  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        ng-disabled="$ctrl.actionInProgress || !$ctrl.registryFormAzure.$valid"
        button-spinner="$ctrl.actionInProgress"
        analytics-on
        analytics-category="portainer"
        analytics-event="portainer-registry-creation"
        analytics-properties="{ metadata: { type: 'azure' } }"
      >
        <span ng-hide="$ctrl.actionInProgress">{{ $ctrl.formActionLabel }}</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</form>
