<form class="form-horizontal" name="endpointGroupForm" ng-if="$ctrl.model">
  <!-- name-input -->
  <div class="form-group">
    <label for="group_name" class="col-sm-3 col-lg-2 control-label required text-left">Name</label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" name="group_name" ng-model="$ctrl.model.Name" placeholder="e.g. my-group" required auto-focus data-cy="group-name-input" />
      <div class="help-block" ng-show="endpointGroupForm.group_name.$invalid">
        <div class="small text-warning">
          <div ng-messages="endpointGroupForm.group_name.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !name-input -->
  <!-- description-input -->
  <div class="form-group">
    <label for="group_description" class="col-sm-3 col-lg-2 control-label text-left">Description</label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="group_description"
        ng-model="$ctrl.model.Description"
        placeholder="e.g. production environments..."
        data-cy="group-description-input"
      />
    </div>
  </div>
  <!-- !description-input -->
  <div class="col-sm-12 form-section-title"> Metadata </div>

  <tag-selector ng-if="$ctrl.model.TagIds" value="$ctrl.model.TagIds" on-change="($ctrl.onChangeTags)" allow-create="$ctrl.state.allowCreateTag"> </tag-selector>

  <div class="form-group" ng-if="$ctrl.model.Id !== 1">
    <associated-endpoints-selector value="$ctrl.associatedEndpoints" on-change="($ctrl.onChangeEnvironments)"></associated-endpoints-selector>
  </div>

  <div class="-mx-[15px]">
    <group-association-table
      ng-if="$ctrl.model.Id === 1"
      title="'Unassociated environments'"
      empty-content-message="'No environment available'"
      query="$ctrl.unassociatedQuery"
    ></group-association-table>
  </div>

  <!-- actions -->
  <div class="col-sm-12 form-section-title"> Actions </div>
  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="button"
        class="btn btn-primary btn-sm"
        ng-click="$ctrl.formAction()"
        ng-disabled="$ctrl.actionInProgress || !endpointGroupForm.$valid"
        button-spinner="$ctrl.actionInProgress"
      >
        <span ng-hide="$ctrl.actionInProgress">{{ $ctrl.formActionLabel }}</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</form>
