<form class="form-horizontal" name="$ctrl.registryFormCustom" ng-submit="$ctrl.formAction()">
  <div class="col-sm-12 form-section-title"> Important notice </div>
  <div class="form-group">
    <span class="col-sm-12 text-muted small">
      Docker requires you to connect to a <a href="https://docs.docker.com/registry/deploying/#running-a-domain-registry" target="_blank">secure registry</a>. You can find more
      information about how to connect to an insecure registry <a href="https://docs.docker.com/registry/insecure/" target="_blank">in the Docker documentation</a>.
    </span>
  </div>
  <div class="col-sm-12 form-section-title"> Custom registry details </div>
  <!-- name-input -->
  <div class="form-group">
    <label for="registry_name" class="col-sm-3 col-lg-2 control-label required text-left">Name</label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_name"
        name="registry_name"
        ng-model="$ctrl.model.Name"
        placeholder="my-custom-registry"
        required
        auto-focus
        data-cy="component-registryNameInput"
      />
      <div class="help-block" ng-show="$ctrl.registryFormCustom.registry_name.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormCustom.registry_name.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
            <p ng-message="used" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              A registry with the same name already exists.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !name-input -->
  <!-- url-input -->
  <div class="form-group">
    <label for="registry_url" class="col-sm-3 col-lg-2 control-label required text-left">
      Registry URL
      <portainer-tooltip message="'URL or IP address of a Docker registry. Any protocol and trailing slash will be stripped if present.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_url"
        name="registry_url"
        ng-model="$ctrl.model.URL"
        placeholder="*********:5000 or myregistry.domain.tld"
        required
        data-cy="component-registryUrlInput"
      />
      <div class="help-block" ng-show="$ctrl.registryFormCustom.registry_url.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormCustom.registry_url.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- url-input -->
  <!-- authentication-checkbox -->
  <div class="form-group">
    <div class="col-sm-12">
      <por-switch-field
        label="'Authentication'"
        label-class="'col-sm-3 col-lg-2'"
        tooltip="'Enable this option if you need to specify credentials to connect to this registry.'"
        name="'administrator'"
        checked="$ctrl.model.Authentication"
        on-change="($ctrl.toggleAuthentication)"
        label-class="'col-sm-2'"
      ></por-switch-field>
    </div>
  </div>
  <!-- !authentication-checkbox -->
  <div ng-if="$ctrl.model.Authentication">
    <!-- credentials-user -->
    <div class="form-group">
      <label for="registry_username" class="col-sm-3 col-lg-2 control-label required text-left">Username</label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          class="form-control"
          id="registry_username"
          name="registry_username"
          ng-model="$ctrl.model.Username"
          required
          data-cy="component-registryUsernameInput"
        />
        <div class="help-block" ng-show="$ctrl.registryFormCustom.registry_username.$invalid">
          <div class="small text-warning">
            <div ng-messages="$ctrl.registryFormCustom.registry_username.$error">
              <p ng-message="required" class="vertical-center">
                <pr-icon icon="'alert-triangle'"></pr-icon>
                This field is required.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- !credentials-user -->
    <!-- credentials-password -->
    <div class="form-group">
      <label for="registry_password" class="col-sm-3 col-lg-2 control-label required text-left">Password</label>
      <div class="col-sm-9 col-lg-10">
        <input type="password" class="form-control" id="registry_password" name="registry_password" ng-model="$ctrl.model.Password" required />
        <div class="help-block" ng-show="$ctrl.registryFormCustom.registry_password.$invalid">
          <div class="small text-warning">
            <div ng-messages="$ctrl.registryFormCustom.registry_password.$error">
              <p ng-message="required" class="vertical-center">
                <pr-icon icon="'alert-triangle'"></pr-icon>
                This field is required.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- !credentials-password -->
  </div>
  <!-- actions -->
  <div class="col-sm-12 form-section-title"> Actions </div>
  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        ng-disabled="$ctrl.actionInProgress || !$ctrl.registryFormCustom.$valid"
        button-spinner="$ctrl.actionInProgress"
        analytics-on
        analytics-category="portainer"
        analytics-event="portainer-registry-creation"
        analytics-properties="{ metadata: { type: 'custom' }}"
      >
        <span ng-hide="$ctrl.actionInProgress">{{ $ctrl.formActionLabel }}</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</form>
