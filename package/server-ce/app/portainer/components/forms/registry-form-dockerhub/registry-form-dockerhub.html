<form class="form-horizontal" name="$ctrl.registryFormDockerhub" ng-submit="$ctrl.formAction()">
  <div class="col-sm-12 form-section-title"> Important notice </div>
  <div class="form-group">
    <span class="col-sm-12 text-muted small">
      <p>
        For information on how to generate a DockerHub Access Token, follow the
        <a href="https://docs.docker.com/docker-hub/access-tokens/" target="_blank">dockerhub guide</a>.
      </p>
    </span>
  </div>
  <div class="col-sm-12 form-section-title"> DockerHub account details </div>
  <!-- name-input -->
  <div class="form-group">
    <label for="registry_name" class="col-sm-3 col-lg-2 control-label required text-left">Name</label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_name"
        name="registry_name"
        ng-model="$ctrl.model.Name"
        placeholder="dockerhub-prod-us"
        required
        data-cy="component-registryName"
      />
      <div class="help-block" ng-show="$ctrl.registryFormDockerhub.registry_name.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormDockerhub.registry_name.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
            <p ng-message="used" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              A registry with the same name already exists.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !name-input -->
  <!-- credentials-user -->
  <div class="form-group">
    <label for="registry_username" class="col-sm-3 col-lg-2 control-label required text-left">DockerHub username</label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" id="registry_username" name="registry_username" ng-model="$ctrl.model.Username" required data-cy="component-registryUsername" />
      <div class="help-block" ng-show="$ctrl.registryFormDockerhub.registry_username.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormDockerhub.registry_username.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-user -->
  <!-- credentials-password -->
  <div class="form-group">
    <label for="registry_password" class="col-sm-3 col-lg-2 control-label required text-left">DockerHub access token</label>
    <div class="col-sm-9 col-lg-10">
      <input type="password" class="form-control" id="registry_password" name="registry_password" ng-model="$ctrl.model.Password" required />
      <div class="help-block" ng-show="$ctrl.registryFormDockerhub.registry_password.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormDockerhub.registry_password.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-password -->

  <!-- actions -->
  <div class="col-sm-12 form-section-title"> Actions </div>
  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        ng-disabled="$ctrl.actionInProgress || !$ctrl.registryFormDockerhub.$valid"
        button-spinner="$ctrl.actionInProgress"
        analytics-on
        analytics-category="portainer"
        analytics-event="portainer-registry-creation"
        analytics-properties="{ metadata: { type: 'dockerhub' } }"
      >
        <span ng-hide="$ctrl.actionInProgress">{{ $ctrl.formActionLabel }}</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</form>
