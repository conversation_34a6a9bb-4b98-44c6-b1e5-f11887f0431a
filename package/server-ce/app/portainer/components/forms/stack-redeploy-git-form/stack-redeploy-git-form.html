<form name="$ctrl.redeployGitForm" class="form-horizontal my-8">
  <div class="col-sm-12 form-section-title"> Redeploy from git repository </div>

  <git-form-info-panel
    class-name="'text-muted small'"
    url="$ctrl.model.URL"
    type="'stack'"
    config-file-path="$ctrl.model.ConfigFilePath"
    additional-files="$ctrl.stack.AdditionalFiles"
  ></git-form-info-panel>

  <git-form-auto-update-fieldset
    value="$ctrl.formValues.AutoUpdate"
    on-change="($ctrl.onChangeAutoUpdate)"
    environment-type="DOCKER"
    is-force-pull-visible="$ctrl.stack.Type !== 3"
    base-webhook-url="{{ $ctrl.state.baseWebhookUrl }}"
    webhook-id="{{ $ctrl.state.webhookId }}"
    webhooks-docs="/user/docker/stacks/webhooks"
  ></git-form-auto-update-fieldset>

  <div class="form-group">
    <div class="col-sm-12">
      <p>
        <a class="small interactive" ng-click="$ctrl.state.showConfig = !$ctrl.state.showConfig">
          <pr-icon ng-if="$ctrl.state.showConfig" icon="'minus'" class-name="'mr-1'"></pr-icon>
          <pr-icon ng-if="!$ctrl.state.showConfig" icon="'plus'" class-name="'mr-1'"></pr-icon>
          {{ $ctrl.state.showConfig ? 'Hide' : 'Advanced' }} configuration
        </a>
      </p>
    </div>
  </div>

  <git-form-ref-field
    ng-if="$ctrl.state.showConfig"
    value="$ctrl.formValues.RefName"
    on-change="($ctrl.onChangeRef)"
    model="$ctrl.formValues"
    is-url-valid="true"
    stack-id="$ctrl.gitStackId"
  ></git-form-ref-field>

  <git-form-auth-fieldset
    ng-if="$ctrl.state.showConfig"
    value="$ctrl.formValues"
    on-change="($ctrl.onChangeGitAuth)"
    is-auth-explanation-visible="true"
    is-auth-edit="$ctrl.state.isAuthEdit"
  ></git-form-auth-fieldset>

  <div class="form-group" ng-if="$ctrl.state.showConfig">
    <div class="col-sm-12">
      <por-switch-field
        name="TLSSkipVerify"
        checked="$ctrl.formValues.TLSSkipVerify"
        tooltip="'Enabling this will allow skipping TLS validation for any self-signed certificate.'"
        label-class="'col-sm-3 col-lg-2'"
        label="'Skip TLS Verification'"
        on-change="($ctrl.onChangeTLSSkipVerify)"
      ></por-switch-field>
    </div>
  </div>

  <div ng-if="$ctrl.state.showConfig">
    <relative-path-fieldset values="$ctrl.stack" git-model="$ctrl.stack" is-editing="true" hide-edge-configs="true"></relative-path-fieldset>
  </div>

  <stack-environment-variables-panel
    values="$ctrl.formValues.Env"
    on-change="($ctrl.onChangeEnvVar)"
    show-help-message="true"
    is-foldable="true"
  ></stack-environment-variables-panel>

  <option-panel ng-if="$ctrl.stack.Type === 1 && $ctrl.endpoint.apiVersion >= 1.27" ng-model="$ctrl.formValues.Option" on-change="($ctrl.onChangeOption)"></option-panel>

  <div class="col-sm-12 form-section-title"> Actions </div>

  <button
    class="btn btn-sm btn-primary"
    ng-click="$ctrl.submit()"
    ng-disabled="$ctrl.disablePullAndRedeployButton()"
    style="margin-top: 7px; margin-left: 0"
    button-spinner="$ctrl.state.redeployInProgress"
    analytics-on
    analytics-event="docker-stack-pull-redeploy"
    analytics-category="docker"
  >
    <span ng-hide="$ctrl.state.redeployInProgress">
      <pr-icon icon="'refresh-cw'" class="!mr-1"></pr-icon>
      Pull and redeploy
    </span>
    <span ng-show="$ctrl.state.redeployInProgress">In progress...</span>
  </button>

  <button
    class="btn btn-sm btn-primary"
    ng-click="$ctrl.saveGitSettings()"
    ng-disabled="$ctrl.disableSaveSettingsButton()"
    style="margin-top: 7px; margin-left: 0"
    button-spinner="$ctrl.state.inProgress"
    analytics-on
    analytics-event="docker-stack-update-git-settings"
    analytics-category="docker"
    analytics-properties="$ctrl.buildAnalyticsProperties()"
  >
    <span ng-hide="$ctrl.state.inProgress"> Save settings </span>
    <span ng-show="$ctrl.state.inProgress">In progress...</span>
  </button>
</form>
