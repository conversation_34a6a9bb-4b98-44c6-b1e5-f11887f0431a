<form name="$ctrl.redeployGitForm">
  <div class="col-sm-12 form-section-title"> Redeploy from git repository </div>
  <div class="form-group text-muted">
    <div class="col-sm-12">
      <p> Pull the latest manifest from git and redeploy the application. </p>
    </div>
  </div>
  <git-form-auto-update-fieldset
    value="$ctrl.formValues.AutoUpdate"
    on-change="($ctrl.onChangeAutoUpdate)"
    environment-type="KUBERNETES"
    is-force-pull-visible="false"
    base-webhook-url="{{ $ctrl.state.baseWebhookUrl }}"
    webhook-id="{{ $ctrl.state.webhookId }}"
    webhooks-docs="/user/kubernetes/applications/webhooks"
  ></git-form-auto-update-fieldset>
  <time-window-display></time-window-display>

  <div class="form-group">
    <div class="col-sm-12">
      <p>
        <a class="small interactive" ng-click="$ctrl.state.showConfig = !$ctrl.state.showConfig">
          <pr-icon ng-if="$ctrl.state.showConfig" icon="'minus'" class-name="'mr-1'"></pr-icon>
          <pr-icon ng-if="!$ctrl.state.showConfig" icon="'plus'" class-name="'mr-1'"></pr-icon>
          {{ $ctrl.state.showConfig ? 'Hide' : 'Advanced' }} configuration
        </a>
      </p>
    </div>
  </div>

  <git-form-ref-field
    ng-if="$ctrl.state.showConfig"
    value="$ctrl.formValues.RefName"
    on-change="($ctrl.onChangeRef)"
    model="$ctrl.formValues"
    is-url-valid="true"
  ></git-form-ref-field>

  <git-form-auth-fieldset
    ng-if="$ctrl.state.showConfig"
    value="$ctrl.formValues"
    on-change="($ctrl.onChangeGitAuth)"
    is-auth-explanation-visible="true"
    is-auth-edit="$ctrl.state.isAuthEdit"
  ></git-form-auth-fieldset>

  <div class="form-group" ng-if="$ctrl.state.showConfig">
    <div class="col-sm-12">
      <por-switch-field
        name="TLSSkipVerify"
        checked="$ctrl.formValues.TLSSkipVerify"
        tooltip="'Enabling this will allow skipping TLS validation for any self-signed certificate.'"
        label-class="'col-sm-3 col-lg-2'"
        label="'Skip TLS Verification'"
        on-change="($ctrl.onChangeTLSSkipVerify)"
      ></por-switch-field>
    </div>
  </div>

  <div class="col-sm-12 form-section-title"> Actions </div>
  <!-- #Git buttons -->
  <button
    class="btn btn-sm btn-primary"
    ng-click="$ctrl.pullAndRedeployApplication()"
    ng-disabled="$ctrl.isSubmitButtonDisabled() || $ctrl.state.hasUnsavedChanges|| !$ctrl.redeployGitForm.$valid"
    style="margin-top: 7px; margin-left: 0"
    button-spinner="$ctrl.state.redeployInProgress"
    analytics-on
    analytics-category="kubernetes"
    analytics-event="kubernetes-application-edit-git-pull"
  >
    <span ng-show="!$ctrl.state.redeployInProgress">
      <pr-icon icon="'refresh-cw'" class="!mr-1"></pr-icon>
      Pull and update application
    </span>
    <span ng-show="$ctrl.state.redeployInProgress">In progress...</span>
  </button>

  <button
    class="btn btn-sm btn-primary"
    ng-click="$ctrl.saveGitSettings()"
    ng-disabled="$ctrl.isSubmitButtonDisabled() || !$ctrl.state.hasUnsavedChanges|| !$ctrl.redeployGitForm.$valid"
    style="margin-top: 7px; margin-left: 0"
    button-spinner="$ctrl.state.saveGitSettingsInProgress"
    analytics-on
    analytics-category="kubernetes"
    analytics-event="kubernetes-application-edit"
    analytics-properties="$ctrl.buildAnalyticsProperties()"
  >
    <span ng-show="!$ctrl.state.saveGitSettingsInProgress"> Save settings </span>
    <span ng-show="$ctrl.state.saveGitSettingsInProgress">In progress...</span>
  </button>
</form>
