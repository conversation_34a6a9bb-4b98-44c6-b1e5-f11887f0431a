<form class="form-horizontal" name="$ctrl.registryFormQuay" ng-submit="$ctrl.formAction()">
  <div class="col-sm-12 form-section-title"> Quay account details </div>
  <!-- name-input -->
  <div class="form-group">
    <label for="registry_name" class="col-sm-3 col-lg-2 control-label required text-left">Name</label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" id="registry_name" name="registry_name" ng-model="$ctrl.model.Name" placeholder="Quay" required data-cy="registry-name-input" />
      <div class="help-block" ng-show="$ctrl.registryFormQuay.registry_name.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormQuay.registry_name.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
            <p ng-message="used" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              A registry with the same name already exists.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !name-input -->
  <!-- credentials-user -->
  <div class="form-group">
    <label for="registry_username" class="col-sm-3 col-lg-2 control-label required text-left">Username</label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_username"
        name="registry_username"
        ng-model="$ctrl.model.Username"
        required
        auto-focus
        data-cy="registry-username-input"
      />
      <div class="help-block" ng-show="$ctrl.registryFormQuay.registry_username.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormQuay.registry_username.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-user -->
  <!-- credentials-password -->
  <div class="form-group">
    <label for="registry_password" class="col-sm-3 col-lg-2 control-label required text-left">Password</label>
    <div class="col-sm-9 col-lg-10">
      <input type="password" class="form-control" id="registry_password" name="registry_password" ng-model="$ctrl.model.Password" required />
      <div class="help-block" ng-show="$ctrl.registryFormQuay.registry_password.$invalid">
        <div class="small text-warning">
          <div ng-messages="$ctrl.registryFormQuay.registry_password.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-password -->

  <!-- organization-checkbox -->
  <div class="form-group">
    <div class="col-sm-12">
      <por-switch-field
        label="'Use organization registry'"
        checked="$ctrl.model.Quay.useOrganisation"
        on-change="($ctrl.toggleOrganisation)"
        label-class="'col-sm-2'"
      ></por-switch-field>
    </div>
  </div>
  <!-- !organisation-checkbox -->
  <div ng-if="$ctrl.model.Quay.useOrganisation">
    <!-- organisation_name -->
    <div class="form-group">
      <label for="organisation_name" class="col-sm-3 col-lg-2 control-label required text-left">Organization name</label>
      <div class="col-sm-9 col-lg-10">
        <input
          type="text"
          class="form-control"
          id="organisation_name"
          name="organisation_name"
          ng-model="$ctrl.model.Quay.organisationName"
          required
          data-cy="registry-organisation-name-input"
        />
        <div class="help-block" ng-show="$ctrl.registryFormQuay.organisation_name.$invalid">
          <div class="small text-warning">
            <div ng-messages="$ctrl.registryFormQuay.organisation_name.$error">
              <p ng-message="required" class="vertical-center">
                <pr-icon icon="'alert-triangle'"></pr-icon>
                This field is required.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- !organisation_name -->
  </div>

  <!-- actions -->
  <div class="col-sm-12 form-section-title"> Actions </div>
  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="submit"
        class="btn btn-primary btn-sm"
        ng-disabled="$ctrl.actionInProgress || !$ctrl.registryFormQuay.$valid"
        button-spinner="$ctrl.actionInProgress"
        analytics-on
        analytics-category="portainer"
        analytics-event="portainer-registry-creation"
        analytics-properties="{ metadata: { type: 'quay' } }"
      >
        <span ng-hide="$ctrl.actionInProgress">{{ $ctrl.formActionLabel }}</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</form>
