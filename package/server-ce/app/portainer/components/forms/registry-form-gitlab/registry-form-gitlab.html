<form class="form-horizontal" name="registryFormGitlab" ng-submit="$ctrl.retrieveRegistries()">
  <div class="col-sm-12 form-section-title"> Important notice </div>
  <div class="form-group">
    <span class="col-sm-12 text-muted small">
      <p>
        For information on how to generate a GitLab Personal Access Token, follow the
        <a href="https://gitlab.com/help/user/profile/personal_access_tokens.md" target="_blank">GitLab guide</a>.
      </p>
    </span>
  </div>
  <div class="col-sm-12 form-section-title"> GitLab registry connection details </div>
  <!-- credentials-user -->
  <div class="form-group">
    <label for="registry_username" class="col-sm-3 col-lg-2 control-label required text-left">Username</label>
    <div class="col-sm-9 col-lg-10">
      <input type="text" class="form-control" id="registry_username" name="registry_username" ng-model="$ctrl.model.Username" required data-cy="registry-username-input" />
      <div class="help-block" ng-show="registryFormGitlab.registry_username.$invalid">
        <div class="small text-warning">
          <div ng-messages="registryFormGitlab.registry_username.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-user -->
  <!-- credentials-pat -->
  <div class="form-group">
    <label for="registry_perso_acc_token" class="col-sm-3 col-lg-2 control-label required text-left">Personal Access Token </label>
    <div class="col-sm-9 col-lg-10">
      <input type="password" class="form-control" id="registry_perso_acc_token" name="registry_perso_acc_token" ng-model="$ctrl.model.Token" required />
      <div class="help-block" ng-show="registryFormGitlab.registry_perso_acc_token.$invalid">
        <div class="small text-warning">
          <div ng-messages="registryFormGitlab.registry_perso_acc_token.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !credentials-pat -->

  <div class="form-group">
    <div class="col-sm-12">
      <button
        type="button"
        class="btn btn-link btn-sm !ml-0 p-0 hover:no-underline"
        ng-if="!$ctrl.state.overrideConfiguration"
        ng-click="$ctrl.state.overrideConfiguration = true;"
      >
        <pr-icon icon="'wrench'"></pr-icon>
        Override default configuration
      </button>
      <button
        type="button"
        class="btn btn-link btn-sm !ml-0 p-0 hover:no-underline"
        ng-if="$ctrl.state.overrideConfiguration"
        ng-click="$ctrl.state.overrideConfiguration = false; $ctrl.resetDefaults()"
      >
        <pr-icon icon="'settings'"></pr-icon>
        Use default configuration
      </button>
    </div>
  </div>

  <!-- url-input -->
  <div class="form-group" ng-if="$ctrl.state.overrideConfiguration">
    <label for="instance_url" class="col-sm-3 col-lg-2 control-label required text-left">
      Instance URL
      <portainer-tooltip message="'URL of Gitlab instance.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="instance_url"
        name="instance_url"
        ng-model="$ctrl.model.Gitlab.InstanceURL"
        placeholder="https://gitlab.com"
        required
        data-cy="instance-url-input"
      />
      <div class="help-block" ng-show="registryFormGitlab.instance_url.$invalid">
        <div class="small text-warning">
          <div ng-messages="registryFormGitlab.instance_url.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !url-input -->
  <!-- url-input -->
  <div class="form-group" ng-if="$ctrl.state.overrideConfiguration">
    <label for="registry_url" class="col-sm-3 col-lg-2 control-label required text-left">
      Registry URL
      <portainer-tooltip message="'URL of Gitlab registry instance.'"></portainer-tooltip>
    </label>
    <div class="col-sm-9 col-lg-10">
      <input
        type="text"
        class="form-control"
        id="registry_url"
        name="registry_url"
        ng-model="$ctrl.model.URL"
        placeholder="https://registry.gitlab.com"
        required
        data-cy="registry-url-input"
      />
      <div class="help-block" ng-show="registryFormGitlab.registry_url.$invalid">
        <div class="small text-warning">
          <div ng-messages="registryFormGitlab.registry_url.$error">
            <p ng-message="required" class="vertical-center">
              <pr-icon icon="'alert-triangle'"></pr-icon>
              This field is required.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- !url-input -->

  <div class="form-group">
    <div class="col-sm-12">
      <button type="submit" class="btn btn-primary btn-sm" ng-disabled="$ctrl.actionInProgress || !registryFormGitlab.$valid" button-spinner="$ctrl.actionInProgress">
        <span ng-hide="$ctrl.actionInProgress">Retrieve projects</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
</form>
<div class="form-horizontal" ng-if="$ctrl.projects">
  <div class="col-sm-12 form-section-title"> GitLab projects </div>
  <div class="form-group">
    <span class="col-sm-12 text-muted small"> Select the project's registries you want to manage. Portainer will create one registry for each selected project. </span>
    <span class="col-sm-12 text-muted small vertical-center">
      <pr-icon icon="'alert-triangle'" class-name="'icon-warning'"></pr-icon>
      If you can't select a project, make sure that registry feature is activated on it.
    </span>
  </div>

  <gitlab-project-selector dataset="$ctrl.projects" value="$ctrl.selectedRegistries" on-change="($ctrl.onChangeRegistries)"></gitlab-project-selector>

  <div class="form-group">
    <div class="col-sm-12">
      <button
        ng-click="$ctrl.createRegistries($ctrl.selectedRegistries)"
        class="btn btn-primary btn-sm"
        ng-disabled="$ctrl.actionInProgress || !$ctrl.selectedRegistries.length"
        button-spinner="$ctrl.actionInProgress"
        analytics-on
        analytics-category="portainer"
        analytics-event="portainer-registry-creation"
        analytics-properties="{ metadata: { type: 'gitlab' } }"
      >
        <span ng-hide="$ctrl.actionInProgress">Create registries</span>
        <span ng-show="$ctrl.actionInProgress">In progress...</span>
      </button>
    </div>
  </div>
  <!-- !actions -->
</div>
