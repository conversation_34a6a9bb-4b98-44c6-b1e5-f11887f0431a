<div authorization="PortainerStackMigrate">
  <div class="col-sm-12 form-section-title"> Stack duplication / migration </div>
  <rd-widget>
    <rd-widget-body>
      <form class="form-horizontal" name="dupStackForm">
        <div class="form-group">
          <span class="small" style="margin-top: 10px">
            <p class="text-muted"> This feature allows you to duplicate or migrate this stack. </p>
          </span>
        </div>

        <div class="form-group">
          <input
            class="form-control"
            placeholder="Stack name (optional for migration)"
            aria-placeholder="Stack name"
            name="new_stack_name"
            ng-pattern="$ctrl.STACK_NAME_VALIDATION_REGEX"
            ng-model="$ctrl.formValues.newName"
          />
        </div>
        <div class="form-group" ng-show="dupStackForm.new_stack_name.$invalid">
          <div class="col-sm-12 small text-warning">
            <div ng-messages="dupStackForm.new_stack_name.$error">
              <p ng-message="pattern">
                <pr-icon icon="'alert-triangle'" mode="'warning'"></pr-icon>
                <span>This field must consist of lower case alphanumeric characters, '_' or '-' (e.g. 'my-name', or 'abc-123').</span>
              </p>
            </div>
          </div>
        </div>

        <div class="form-group" ng-if="$ctrl.endpoints && $ctrl.groups">
          <por-select value="$ctrl.formValues.endpointId" on-change="($ctrl.onChangeEnvironment)" options="$ctrl.environmentSelectorOptions"></por-select>
        </div>

        <div class="form-group">
          <button
            class="btn btn-sm btn-primary"
            ng-click="$ctrl.migrateStack()"
            ng-disabled="$ctrl.isMigrationButtonDisabled()"
            style="margin-top: 7px; margin-left: 0"
            button-spinner="$ctrl.state.migrationInProgress"
          >
            <span ng-hide="$ctrl.state.migrationInProgress">
              <pr-icon icon="'arrow-right'" class-name="'mr-1'"></pr-icon>
              Migrate
            </span>
            <span ng-show="$ctrl.state.migrationInProgress">Migration in progress...</span>
          </button>
          <button
            class="btn btn-sm btn-primary"
            ng-click="$ctrl.duplicateStack()"
            ng-disabled="!$ctrl.isFormValidForDuplication() || $ctrl.state.duplicationInProgress || $ctrl.state.migrationInProgress"
            style="margin-top: 7px; margin-left: 0"
            button-spinner="$ctrl.state.duplicationInProgress"
          >
            <span ng-hide="$ctrl.state.duplicationInProgress">
              <pr-icon icon="'copy'" class-name="'space-right'"></pr-icon>
              Duplicate
            </span>
            <span ng-show="$ctrl.state.duplicationInProgress">Duplication in progress...</span>
          </button>
        </div>

        <div class="form-group">
          <div ng-if="$ctrl.yamlError && $ctrl.isEndpointSelected()">
            <span class="text-danger small">{{ $ctrl.yamlError }}</span>
          </div>
        </div>
      </form>
    </rd-widget-body>
  </rd-widget>
</div>
