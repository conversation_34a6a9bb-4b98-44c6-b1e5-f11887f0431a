<div>
  <div class="form-group pt-3">
    <label for="stack_template" class="col-sm-3 col-lg-2 control-label text-left"> Template </label>
    <div class="col-sm-9 col-lg-10 flex flex-col gap-y-1">
      <select
        ng-if="$ctrl.templates.length"
        data-cy="custom-template-selector"
        class="form-control"
        ng-model="$ctrl.value"
        ng-options="template.Id as template.label for template in $ctrl.templates"
        ng-change="$ctrl.handleChangeTemplate($ctrl.value)"
      >
        <option value="" label="Select a Custom Template" disabled selected="selected"> </option>
      </select>
      <span ng-if="$ctrl.isLoadFailed">
        <p class="text-warning mb-5 !inline-flex gap-1 !align-top text-xs" ng-if="ctrl.currentUser.isAdmin || ctrl.currentUser.id === ctrl.state.template.CreatedByUserId">
          <pr-icon icon="'alert-triangle'" mode="'warning'" size="'sm'"></pr-icon>Custom template could not be loaded, please
          <a ui-sref="kubernetes.templates.custom.edit({id: ctrl.state.templateId})">click here</a> for configuration.</p
        >
        <p class="text-warning mb-5 !inline-flex gap-1 !align-top text-xs" ng-if="!(ctrl.currentUser.isAdmin || ctrl.currentUser.id === ctrl.state.template.CreatedByUserId)">
          <pr-icon icon="'alert-triangle'" mode="'warning'" size="'sm'"></pr-icon>Custom template could not be loaded, please contact your administrator.</p
        >
      </span>
      <span class="small text-muted pt-[7px]" ng-if="!$ctrl.templates.length">
        No custom templates are available. Head over to the <a ui-state="$ctrl.newTemplatePath">custom template view</a> to create one.
      </span>
    </div>
  </div>

  <!-- description -->
  <div ng-if="$ctrl.selectedTemplate.note">
    <div class="col-sm-12 form-section-title"> Information </div>
    <div class="form-group">
      <div class="col-sm-12">
        <div class="template-note" ng-bind-html="$ctrl.selectedTemplate.note"></div>
      </div>
    </div>
  </div>
  <!-- !description -->
</div>
