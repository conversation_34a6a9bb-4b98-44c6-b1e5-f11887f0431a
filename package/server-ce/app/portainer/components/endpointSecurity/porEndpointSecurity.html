<div>
  <!-- tls-checkbox -->
  <div class="form-group">
    <div class="col-sm-12">
      <por-switch-field
        label="'TLS'"
        label-class="'col-sm-2'"
        checked="$ctrl.formData.TLS"
        on-change="($ctrl.onToggleTLS)"
        tooltip="'Enable this option if you need to connect to the Docker environment with TLS.'"
      ></por-switch-field>
    </div>
  </div>
  <!-- !tls-checkbox -->
  <div class="col-sm-12 form-section-title" ng-if="$ctrl.formData.TLS"> TLS mode </div>
  <!-- note -->
  <div class="form-group" ng-if="$ctrl.formData.TLS">
    <div class="col-sm-12">
      <span class="small text-muted">
        You can find out more information about how to protect a Docker environment with TLS in the
        <a href="https://docs.docker.com/engine/security/https/" target="_blank">Docker documentation</a>.
      </span>
    </div>
  </div>

  <box-selector
    ng-if="$ctrl.formData.TLS"
    slim="true"
    radio-name="'tls_mode'"
    options="$ctrl.tlsOptions"
    value="$ctrl.formData.TLSMode"
    on-change="($ctrl.onChangeTLSMode)"
  ></box-selector>

  <div class="col-sm-12 form-section-title" ng-if="$ctrl.formData.TLS && $ctrl.formData.TLSMode !== 'tls_only'"> Required TLS files </div>
  <!-- tls-file-upload -->
  <div ng-if="$ctrl.formData.TLS">
    <!-- tls-file-ca -->
    <div class="form-group" ng-if="$ctrl.formData.TLSMode === 'tls_client_ca' || $ctrl.formData.TLSMode === 'tls_ca'">
      <label class="col-sm-3 col-lg-2 control-label text-left">TLS CA certificate</label>
      <div class="col-sm-9 col-lg-10">
        <button type="button" class="btn btn-sm btn-primary" ngf-select ng-model="$ctrl.formData.TLSCACert">Select file</button>
        <span class="space-left">
          {{ $ctrl.formData.TLSCACert.name }}
          <pr-icon icon="'check'" ng-if="$ctrl.formData.TLSCACert && $ctrl.formData.TLSCACert === $ctrl.endpoint.TLSConfig.TLSCACert" mode="'success'"></pr-icon>
          <pr-icon icon="'x'" ng-if="!$ctrl.formData.TLSCACert" mode="'danger'"></pr-icon>
        </span>
      </div>
    </div>
    <!-- !tls-file-ca -->
    <!-- tls-files-cert-key -->
    <div ng-if="$ctrl.formData.TLSMode === 'tls_client_ca' || $ctrl.formData.TLSMode === 'tls_client_noca'">
      <!-- tls-file-cert -->
      <div class="form-group">
        <label for="tls_cert" class="col-sm-3 col-lg-2 control-label text-left">TLS certificate</label>
        <div class="col-sm-9 col-lg-10">
          <button type="button" class="btn btn-sm btn-primary" ngf-select ng-model="$ctrl.formData.TLSCert">Select file</button>
          <span class="space-left">
            {{ $ctrl.formData.TLSCert.name }}
            <pr-icon icon="'check'" ng-if="$ctrl.formData.TLSCert && $ctrl.formData.TLSCert === $ctrl.endpoint.TLSConfig.TLSCert" mode="'success'"></pr-icon>
            <pr-icon icon="'x'" ng-if="!$ctrl.formData.TLSCert" mode="'danger'"></pr-icon>
          </span>
        </div>
      </div>
      <!-- !tls-file-cert -->
      <!-- tls-file-key -->
      <div class="form-group">
        <label class="col-sm-3 col-lg-2 control-label text-left">TLS key</label>
        <div class="col-sm-9 col-lg-10">
          <button type="button" class="btn btn-sm btn-primary" ngf-select ng-model="$ctrl.formData.TLSKey">Select file</button>
          <span class="space-left">
            {{ $ctrl.formData.TLSKey.name }}
            <pr-icon icon="'check'" ng-if="$ctrl.formData.TLSKey && $ctrl.formData.TLSKey === $ctrl.endpoint.TLSConfig.TLSKey" mode="'success'"></pr-icon>
            <pr-icon icon="'x'" ng-if="!$ctrl.formData.TLSKey" mode="'danger'"></pr-icon>
          </span>
        </div>
      </div>
      <!-- !tls-file-key -->
    </div>
    <!-- tls-files-cert-key -->
  </div>
  <!-- !tls-file-upload -->
</div>
