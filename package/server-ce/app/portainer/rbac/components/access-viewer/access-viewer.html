<div class="col-sm-12" style="margin-bottom: 0px">
  <div class="be-indicator-container limited-be">
    <div class="limited-be-link vertical-center"><be-feature-indicator feature="$ctrl.limitedFeature"></be-feature-indicator></div>
    <div class="limited-be-content">
      <rd-widget>
        <rd-widget-header icon="user-x">
          <header-title> Effective access viewer </header-title>
        </rd-widget-header>
        <rd-widget-body>
          <form class="form-horizontal">
            <div class="col-sm-12 form-section-title"> User </div>
            <div class="form-group">
              <div class="col-sm-12">
                <span class="small text-muted" ng-if="$ctrl.users.length === 0"> No user available </span>

                <por-select ng-if="$ctrl.users.length > 0" value="$ctrl.selectedUserId" options="$ctrl.users" on-change="($ctrl.onUserSelect)" placeholder="'Select a user'">
                </por-select>
              </div>
            </div>
            <div ng-if="$ctrl.selectedUserId">
              <effective-access-viewer-datatable ng-if="$ctrl.selectedUserId" dataset="$ctrl.userRoles"> </effective-access-viewer-datatable>
            </div>
          </form>
        </rd-widget-body>
      </rd-widget>
    </div>
  </div>
</div>
