import toastr from 'toastr';

import { notifyError, notify<PERSON>uc<PERSON>, notifyWarning } from './notifications';

vi.spyOn(console, 'error').mockImplementation(() => vi.fn());

afterEach(() => {
  vi.resetAllMocks();
});

it('calling success should show success message', () => {
  const title = 'title';
  const text = 'text';

  notifySuccess(title, text);

  expect(toastr.success).toHaveBeenCalledWith(text, title);
});

it('calling error with Error should show error message', () => {
  const consoleErrorFn = vi
    .spyOn(console, 'error')
    .mockImplementation(() => vi.fn());
  const title = 'title';
  const errorMessage = 'message';
  const fallback = 'fallback';

  notifyError(title, new Error(errorMessage), fallback);

  expect(toastr.error).toHaveBeenCalledWith(
    errorMessage,
    title,
    expect.anything()
  );

  consoleErrorFn.mockRestore();
});

it('calling error without Error should show fallback message', () => {
  const consoleErrorFn = vi
    .spyOn(console, 'error')
    .mockImplementation(() => vi.fn());
  const title = 'title';

  const fallback = 'fallback';

  notifyError(title, undefined, fallback);

  expect(toastr.error).toHaveBeenCalledWith(fallback, title, expect.anything());
  consoleErrorFn.mockRestore();
});

it('calling warning should show warning message', () => {
  const title = 'title';
  const text = 'text';

  notifyWarning(title, text);

  expect(toastr.warning).toHaveBeenCalledWith(text, title, expect.anything());
});
