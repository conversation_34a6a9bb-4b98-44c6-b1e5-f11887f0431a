package cli

const (
	defaultBindAddress            = ":9000"
	defaultHTTPSBindAddress       = ":9443"
	defaultTunnelServerAddress    = "0.0.0.0"
	defaultTunnelServerPort       = "8000"
	defaultDataDirectory          = "C:\\data"
	defaultAssetsDirectory        = "./"
	defaultTLS                    = "false"
	defaultTLSSkipVerify          = "false"
	defaultTLSCACertPath          = "C:\\certs\\ca.pem"
	defaultTLSCertPath            = "C:\\certs\\cert.pem"
	defaultTLSKeyPath             = "C:\\certs\\key.pem"
	defaultHTTPDisabled           = "false"
	defaultHTTPEnabled            = "false"
	defaultSSL                    = "false"
	defaultSnapshotInterval       = "5m"
	defaultBaseURL                = "/"
	defaultSecretKeyName          = "portainer"
	defaultPullLimitCheckDisabled = "false"
)
