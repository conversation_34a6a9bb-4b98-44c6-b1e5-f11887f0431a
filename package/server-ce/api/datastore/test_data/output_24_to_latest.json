{"api_key": null, "customtemplates": null, "dockerhub": [{"Authentication": false, "Username": ""}], "edge_stack": null, "edge_stack_status": null, "edgegroups": null, "edgejobs": null, "endpoint_groups": [{"AuthorizedTeams": null, "AuthorizedUsers": null, "Description": "Unassigned endpoints", "Id": 1, "Labels": [], "Name": "Unassigned", "TagIds": [], "Tags": null, "TeamAccessPolicies": {}, "UserAccessPolicies": {}}], "endpoint_relations": [{"EdgeStacks": {}, "EndpointID": 1}], "endpoints": [{"Agent": {"Version": ""}, "AuthorizedTeams": null, "AuthorizedUsers": null, "AzureCredentials": {"ApplicationID": "", "AuthenticationKey": "", "TenantID": ""}, "ComposeSyntaxMaxVersion": "", "ContainerEngine": "", "Edge": {"AsyncMode": false, "CommandInterval": 0, "PingInterval": 0, "SnapshotInterval": 0}, "EdgeCheckinInterval": 0, "EdgeKey": "", "Gpus": [], "GroupId": 1, "Heartbeat": false, "Id": 1, "Kubernetes": {"Configuration": {"AllowNoneIngressClass": false, "EnableResourceOverCommit": false, "IngressAvailabilityPerNamespace": true, "IngressClasses": null, "ResourceOverCommitPercentage": 0, "RestrictDefaultNamespace": false, "StorageClasses": null, "UseLoadBalancer": false, "UseServerMetrics": false}, "Flags": {"IsServerIngressClassDetected": false, "IsServerMetricsDetected": false, "IsServerStorageDetected": false}, "Snapshots": []}, "LastCheckInDate": 0, "Name": "local", "PostInitMigrations": {"MigrateGPUs": true, "MigrateIngresses": true}, "PublicURL": "", "QueryDate": 0, "SecuritySettings": {"allowBindMountsForRegularUsers": true, "allowContainerCapabilitiesForRegularUsers": true, "allowDeviceMappingForRegularUsers": true, "allowHostNamespaceForRegularUsers": true, "allowPrivilegedModeForRegularUsers": true, "allowStackManagementForRegularUsers": true, "allowSysctlSettingForRegularUsers": false, "allowVolumeBrowserForRegularUsers": false, "enableHostManagementFeatures": false}, "Snapshots": [], "Status": 1, "TLSConfig": {"TLS": false, "TLSSkipVerify": false}, "TagIds": [], "Tags": null, "TeamAccessPolicies": {}, "Type": 1, "URL": "unix:///var/run/docker.sock", "UserAccessPolicies": {}}], "extension": null, "helm_user_repository": null, "pending_actions": null, "registries": [{"Authentication": true, "AuthorizedTeams": null, "AuthorizedUsers": null, "BaseURL": "", "Ecr": {"Region": ""}, "Github": {"OrganisationName": "", "UseOrganisation": false}, "Gitlab": {"InstanceURL": "", "ProjectId": 0, "ProjectPath": ""}, "Id": 1, "ManagementConfiguration": null, "Name": "canister.io", "Password": "MjWbx8A6YK7cw7", "Quay": {"OrganisationName": ""}, "RegistryAccesses": {"1": {"Namespaces": [], "TeamAccessPolicies": {}, "UserAccessPolicies": {}}}, "TeamAccessPolicies": {}, "Type": 3, "URL": "cloud.canister.io:5000", "UserAccessPolicies": {}, "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "resource_control": [{"AdministratorsOnly": false, "Id": 2, "Public": true, "ResourceId": "762gbwaj8r4gcsdy8ld1u4why", "SubResourceIds": [], "System": false, "TeamAccesses": [], "Type": 5, "UserAccesses": []}, {"AdministratorsOnly": false, "Id": 3, "Public": true, "ResourceId": "1_alpine", "SubResourceIds": [], "System": false, "TeamAccesses": [], "Type": 6, "UserAccesses": []}, {"AdministratorsOnly": false, "Id": 4, "Public": true, "ResourceId": "1_redis", "SubResourceIds": [], "System": false, "TeamAccesses": [], "Type": 6, "UserAccesses": []}, {"AdministratorsOnly": false, "Id": 5, "Public": false, "ResourceId": "1_nginx", "SubResourceIds": [], "System": false, "TeamAccesses": [{"AccessLevel": 1, "TeamId": 1}], "Type": 6, "UserAccesses": []}], "roles": [{"Authorizations": {"DockerAgentBrowseDelete": true, "DockerAgentBrowseGet": true, "DockerAgentBrowseList": true, "DockerAgentBrowsePut": true, "DockerAgentBrowseRename": true, "DockerAgentHostInfo": true, "DockerAgentList": true, "DockerAgentPing": true, "DockerAgentUndefined": true, "DockerBuildCancel": true, "DockerBuildPrune": true, "DockerConfigCreate": true, "DockerConfigDelete": true, "DockerConfigInspect": true, "DockerConfigList": true, "DockerConfigUpdate": true, "DockerContainerArchive": true, "DockerContainerArchiveInfo": true, "DockerContainerAttach": true, "DockerContainerAttachWebsocket": true, "DockerContainerChanges": true, "DockerContainerCreate": true, "DockerContainerDelete": true, "DockerContainerExec": true, "DockerContainerExport": true, "DockerContainerInspect": true, "DockerContainerKill": true, "DockerContainerList": true, "DockerContainerLogs": true, "DockerContainerPause": true, "DockerContainerPrune": true, "DockerContainerPutContainerArchive": true, "DockerContainerRename": true, "DockerContainerResize": true, "DockerContainerRestart": true, "DockerContainerStart": true, "DockerContainerStats": true, "DockerContainerStop": true, "DockerContainerTop": true, "DockerContainerUnpause": true, "DockerContainerUpdate": true, "DockerContainerWait": true, "DockerDistributionInspect": true, "DockerEvents": true, "DockerExecInspect": true, "DockerExecResize": true, "DockerExecStart": true, "DockerImageBuild": true, "DockerImageCommit": true, "DockerImageCreate": true, "DockerImageDelete": true, "DockerImageGet": true, "DockerImageGetAll": true, "DockerImageHistory": true, "DockerImageInspect": true, "DockerImageList": true, "DockerImageLoad": true, "DockerImagePrune": true, "DockerImagePush": true, "DockerImageSearch": true, "DockerImageTag": true, "DockerInfo": true, "DockerNetworkConnect": true, "DockerNetworkCreate": true, "DockerNetworkDelete": true, "DockerNetworkDisconnect": true, "DockerNetworkInspect": true, "DockerNetworkList": true, "DockerNetworkPrune": true, "DockerNodeDelete": true, "DockerNodeInspect": true, "DockerNodeList": true, "DockerNodeUpdate": true, "DockerPing": true, "DockerPluginCreate": true, "DockerPluginDelete": true, "DockerPluginDisable": true, "DockerPluginEnable": true, "DockerPluginInspect": true, "DockerPluginList": true, "DockerPluginPrivileges": true, "DockerPluginPull": true, "DockerPluginPush": true, "DockerPluginSet": true, "DockerPluginUpgrade": true, "DockerSecretCreate": true, "DockerSecretDelete": true, "DockerSecretInspect": true, "DockerSecretList": true, "DockerSecretUpdate": true, "DockerServiceCreate": true, "DockerServiceDelete": true, "DockerServiceInspect": true, "DockerServiceList": true, "DockerServiceLogs": true, "DockerServiceUpdate": true, "DockerSessionStart": true, "DockerSwarmInit": true, "DockerSwarmInspect": true, "DockerSwarmJoin": true, "DockerSwarmLeave": true, "DockerSwarmUnlock": true, "DockerSwarmUnlockKey": true, "DockerSwarmUpdate": true, "DockerSystem": true, "DockerTaskInspect": true, "DockerTaskList": true, "DockerTaskLogs": true, "DockerUndefined": true, "DockerVersion": true, "DockerVolumeCreate": true, "DockerVolumeDelete": true, "DockerVolumeInspect": true, "DockerVolumeList": true, "DockerVolumePrune": true, "EndpointResourcesAccess": true, "IntegrationStoridgeAdmin": true, "PortainerResourceControlCreate": true, "PortainerResourceControlUpdate": true, "PortainerStackCreate": true, "PortainerStackDelete": true, "PortainerStackFile": true, "PortainerStackInspect": true, "PortainerStackList": true, "PortainerStackMigrate": true, "PortainerStackUpdate": true, "PortainerWebhookCreate": true, "PortainerWebhookDelete": true, "PortainerWebhookList": true, "PortainerWebsocketExec": true}, "Description": "Full control of all resources in an endpoint", "Id": 1, "Name": "Endpoint administrator", "Priority": 1}, {"Authorizations": {"DockerAgentHostInfo": true, "DockerAgentList": true, "DockerAgentPing": true, "DockerConfigInspect": true, "DockerConfigList": true, "DockerContainerArchiveInfo": true, "DockerContainerChanges": true, "DockerContainerInspect": true, "DockerContainerList": true, "DockerContainerLogs": true, "DockerContainerStats": true, "DockerContainerTop": true, "DockerDistributionInspect": true, "DockerEvents": true, "DockerImageGet": true, "DockerImageGetAll": true, "DockerImageHistory": true, "DockerImageInspect": true, "DockerImageList": true, "DockerImageSearch": true, "DockerInfo": true, "DockerNetworkInspect": true, "DockerNetworkList": true, "DockerNodeInspect": true, "DockerNodeList": true, "DockerPing": true, "DockerPluginList": true, "DockerSecretInspect": true, "DockerSecretList": true, "DockerServiceInspect": true, "DockerServiceList": true, "DockerServiceLogs": true, "DockerSwarmInspect": true, "DockerSystem": true, "DockerTaskInspect": true, "DockerTaskList": true, "DockerTaskLogs": true, "DockerVersion": true, "DockerVolumeInspect": true, "DockerVolumeList": true, "EndpointResourcesAccess": true, "PortainerStackFile": true, "PortainerStackInspect": true, "PortainerStackList": true, "PortainerWebhookList": true}, "Description": "Read-only access of all resources in an endpoint", "Id": 2, "Name": "Helpdesk", "Priority": 2}, {"Authorizations": {"DockerAgentHostInfo": true, "DockerAgentList": true, "DockerAgentPing": true, "DockerAgentUndefined": true, "DockerBuildCancel": true, "DockerBuildPrune": true, "DockerConfigCreate": true, "DockerConfigDelete": true, "DockerConfigInspect": true, "DockerConfigList": true, "DockerConfigUpdate": true, "DockerContainerArchive": true, "DockerContainerArchiveInfo": true, "DockerContainerAttach": true, "DockerContainerAttachWebsocket": true, "DockerContainerChanges": true, "DockerContainerCreate": true, "DockerContainerDelete": true, "DockerContainerExec": true, "DockerContainerExport": true, "DockerContainerInspect": true, "DockerContainerKill": true, "DockerContainerList": true, "DockerContainerLogs": true, "DockerContainerPause": true, "DockerContainerPutContainerArchive": true, "DockerContainerRename": true, "DockerContainerResize": true, "DockerContainerRestart": true, "DockerContainerStart": true, "DockerContainerStats": true, "DockerContainerStop": true, "DockerContainerTop": true, "DockerContainerUnpause": true, "DockerContainerUpdate": true, "DockerContainerWait": true, "DockerDistributionInspect": true, "DockerEvents": true, "DockerExecInspect": true, "DockerExecResize": true, "DockerExecStart": true, "DockerImageBuild": true, "DockerImageCommit": true, "DockerImageCreate": true, "DockerImageDelete": true, "DockerImageGet": true, "DockerImageGetAll": true, "DockerImageHistory": true, "DockerImageInspect": true, "DockerImageList": true, "DockerImageLoad": true, "DockerImagePush": true, "DockerImageSearch": true, "DockerImageTag": true, "DockerInfo": true, "DockerNetworkConnect": true, "DockerNetworkCreate": true, "DockerNetworkDelete": true, "DockerNetworkDisconnect": true, "DockerNetworkInspect": true, "DockerNetworkList": true, "DockerNodeDelete": true, "DockerNodeInspect": true, "DockerNodeList": true, "DockerNodeUpdate": true, "DockerPing": true, "DockerPluginCreate": true, "DockerPluginDelete": true, "DockerPluginDisable": true, "DockerPluginEnable": true, "DockerPluginInspect": true, "DockerPluginList": true, "DockerPluginPrivileges": true, "DockerPluginPull": true, "DockerPluginPush": true, "DockerPluginSet": true, "DockerPluginUpgrade": true, "DockerSecretCreate": true, "DockerSecretDelete": true, "DockerSecretInspect": true, "DockerSecretList": true, "DockerSecretUpdate": true, "DockerServiceCreate": true, "DockerServiceDelete": true, "DockerServiceInspect": true, "DockerServiceList": true, "DockerServiceLogs": true, "DockerServiceUpdate": true, "DockerSessionStart": true, "DockerSwarmInit": true, "DockerSwarmInspect": true, "DockerSwarmJoin": true, "DockerSwarmLeave": true, "DockerSwarmUnlock": true, "DockerSwarmUnlockKey": true, "DockerSwarmUpdate": true, "DockerSystem": true, "DockerTaskInspect": true, "DockerTaskList": true, "DockerTaskLogs": true, "DockerUndefined": true, "DockerVersion": true, "DockerVolumeCreate": true, "DockerVolumeDelete": true, "DockerVolumeInspect": true, "DockerVolumeList": true, "PortainerResourceControlUpdate": true, "PortainerStackCreate": true, "PortainerStackDelete": true, "PortainerStackFile": true, "PortainerStackInspect": true, "PortainerStackList": true, "PortainerStackMigrate": true, "PortainerStackUpdate": true, "PortainerWebhookCreate": true, "PortainerWebhookList": true, "PortainerWebsocketExec": true}, "Description": "Full control of assigned resources in an endpoint", "Id": 3, "Name": "Standard user", "Priority": 3}, {"Authorizations": {"DockerAgentHostInfo": true, "DockerAgentList": true, "DockerAgentPing": true, "DockerConfigInspect": true, "DockerConfigList": true, "DockerContainerArchiveInfo": true, "DockerContainerChanges": true, "DockerContainerInspect": true, "DockerContainerList": true, "DockerContainerLogs": true, "DockerContainerStats": true, "DockerContainerTop": true, "DockerDistributionInspect": true, "DockerEvents": true, "DockerImageGet": true, "DockerImageGetAll": true, "DockerImageHistory": true, "DockerImageInspect": true, "DockerImageList": true, "DockerImageSearch": true, "DockerInfo": true, "DockerNetworkInspect": true, "DockerNetworkList": true, "DockerNodeInspect": true, "DockerNodeList": true, "DockerPing": true, "DockerPluginList": true, "DockerSecretInspect": true, "DockerSecretList": true, "DockerServiceInspect": true, "DockerServiceList": true, "DockerServiceLogs": true, "DockerSwarmInspect": true, "DockerSystem": true, "DockerTaskInspect": true, "DockerTaskList": true, "DockerTaskLogs": true, "DockerVersion": true, "DockerVolumeInspect": true, "DockerVolumeList": true, "PortainerStackFile": true, "PortainerStackInspect": true, "PortainerStackList": true, "PortainerWebhookList": true}, "Description": "Read-only access of assigned resources in an endpoint", "Id": 4, "Name": "Read-only user", "Priority": 4}], "schedules": [{"Created": 1648608136, "CronExpression": "@every 5m", "EdgeSchedule": null, "EndpointSyncJob": null, "Id": 1, "JobType": 2, "Name": "system_snapshot", "Recurring": true, "ScriptExecutionJob": null, "SnapshotJob": {}}], "settings": {"AgentSecret": "", "AllowBindMountsForRegularUsers": true, "AllowContainerCapabilitiesForRegularUsers": true, "AllowDeviceMappingForRegularUsers": true, "AllowHostNamespaceForRegularUsers": true, "AllowPrivilegedModeForRegularUsers": true, "AllowStackManagementForRegularUsers": true, "AuthenticationMethod": 1, "BlackListedLabels": [], "Edge": {"CommandInterval": 0, "PingInterval": 0, "SnapshotInterval": 0}, "EdgeAgentCheckinInterval": 5, "EdgePortainerUrl": "", "EnableEdgeComputeFeatures": false, "EnableTelemetry": true, "EnforceEdgeID": false, "FeatureFlagSettings": null, "GlobalDeploymentOptions": {"hideStacksFunctionality": false}, "HelmRepositoryURL": "https://charts.bitnami.com/bitnami", "InternalAuthSettings": {"RequiredPasswordLength": 12}, "KubeconfigExpiry": "0", "KubectlShellImage": "portainer/kubectl-shell:2.33.0-rc1", "LDAPSettings": {"AnonymousMode": true, "AutoCreateUsers": true, "GroupSearchSettings": [{"GroupAttribute": "", "GroupBaseDN": "", "GroupFilter": ""}], "ReaderDN": "", "SearchSettings": [{"BaseDN": "", "Filter": "", "UserNameAttribute": ""}], "StartTLS": false, "TLSConfig": {"TLS": false, "TLSSkipVerify": false}, "URL": ""}, "LogoURL": "", "OAuthSettings": {"AccessTokenURI": "", "AuthStyle": 0, "AuthorizationURI": "", "ClientID": "", "DefaultTeamID": 0, "KubeSecretKey": null, "LogoutURI": "", "OAuthAutoCreateUsers": false, "RedirectURI": "", "ResourceURI": "", "SSO": false, "Scopes": "", "UserIdentifier": ""}, "SnapshotInterval": "5m", "TemplatesURL": "", "TrustOnFirstConnect": false, "UserSessionTimeout": "8h", "openAMTConfiguration": {"certFileContent": "", "certFileName": "", "certFilePassword": "", "domainName": "", "enabled": false, "mpsPassword": "", "mpsServer": "", "mpsToken": "", "mpsUser": ""}}, "snapshots": [{"Docker": {"ContainerCount": 0, "DiagnosticsData": {}, "DockerSnapshotRaw": {"Containers": null, "Images": null, "Info": {"Architecture": "", "CDISpecDirs": null, "CPUSet": false, "CPUShares": false, "CgroupDriver": "", "ContainerdCommit": {"ID": ""}, "Containers": 0, "ContainersPaused": 0, "ContainersRunning": 0, "ContainersStopped": 0, "CpuCfsPeriod": false, "CpuCfsQuota": false, "Debug": false, "DefaultRuntime": "", "DockerRootDir": "", "Driver": "", "DriverStatus": null, "ExperimentalBuild": false, "GenericResources": null, "HttpProxy": "", "HttpsProxy": "", "ID": "", "IPv4Forwarding": false, "Images": 0, "IndexServerAddress": "", "InitBinary": "", "InitCommit": {"ID": ""}, "Isolation": "", "KernelVersion": "", "Labels": null, "LiveRestoreEnabled": false, "LoggingDriver": "", "MemTotal": 0, "MemoryLimit": false, "NCPU": 0, "NEventsListener": 0, "NFd": 0, "NGoroutines": 0, "Name": "", "NoProxy": "", "OSType": "", "OSVersion": "", "OomKillDisable": false, "OperatingSystem": "", "PidsLimit": false, "Plugins": {"Authorization": null, "Log": null, "Network": null, "Volume": null}, "RegistryConfig": null, "RuncCommit": {"ID": ""}, "Runtimes": null, "SecurityOptions": null, "ServerVersion": "", "SwapLimit": false, "Swarm": {"ControlAvailable": false, "Error": "", "LocalNodeState": "", "NodeAddr": "", "NodeID": "", "RemoteManagers": null}, "SystemTime": "", "Warnings": null}, "Networks": null, "Version": {"ApiVersion": "", "Arch": "", "GitCommit": "", "GoVersion": "", "Os": "", "Platform": {"Name": ""}, "Version": ""}, "Volumes": {"Volumes": null, "Warnings": null}}, "DockerVersion": "20.10.13", "GpuUseAll": false, "GpuUseList": null, "HealthyContainerCount": 0, "ImageCount": 9, "IsPodman": false, "NodeCount": 0, "PerformanceMetrics": null, "RunningContainerCount": 5, "ServiceCount": 0, "StackCount": 2, "StoppedContainerCount": 0, "Swarm": false, "Time": **********, "TotalCPU": 8, "TotalMemory": 25098706944, "UnhealthyContainerCount": 0, "VolumeCount": 10}, "EndpointId": 1, "Kubernetes": null}], "ssl": {"certPath": "", "httpEnabled": true, "keyPath": "", "selfSigned": false}, "stacks": [{"AdditionalFiles": null, "AutoUpdate": null, "CreatedBy": "", "CreationDate": 0, "EndpointId": 1, "EntryPoint": "docker/alpine37-compose.yml", "Env": [], "FromAppTemplate": false, "GitConfig": null, "Id": 2, "Name": "alpine", "Namespace": "", "Option": null, "ProjectPath": "/home/<USER>/portainer/data/ce1.25/compose/2", "ResourceControl": null, "Status": 1, "SwarmId": "s3fd604zdba7z13tbq2x6lyue", "Type": 1, "UpdateDate": 0, "UpdatedBy": ""}, {"AdditionalFiles": null, "AutoUpdate": null, "CreatedBy": "", "CreationDate": 0, "EndpointId": 1, "EntryPoint": "docker-compose.yml", "Env": [], "FromAppTemplate": false, "GitConfig": null, "Id": 5, "Name": "redis", "Namespace": "", "Option": null, "ProjectPath": "/home/<USER>/portainer/data/ce1.25/compose/5", "ResourceControl": null, "Status": 1, "SwarmId": "", "Type": 2, "UpdateDate": 0, "UpdatedBy": ""}, {"AdditionalFiles": null, "AutoUpdate": null, "CreatedBy": "", "CreationDate": 0, "EndpointId": 1, "EntryPoint": "docker-compose.yml", "Env": [], "FromAppTemplate": false, "GitConfig": null, "Id": 6, "Name": "nginx", "Namespace": "", "Option": null, "ProjectPath": "/home/<USER>/portainer/data/ce1.25/compose/6", "ResourceControl": null, "Status": 1, "SwarmId": "", "Type": 2, "UpdateDate": 0, "UpdatedBy": ""}], "tags": null, "team_membership": null, "teams": [{"Id": 1, "Name": "hello"}], "tunnel_server": {"PrivateKeySeed": ""}, "users": [{"EndpointAuthorizations": null, "Id": 1, "Password": "$2a$10$siRDprr/5uUFAU8iom3Sr./WXQkN2dhSNjAC471pkJaALkghS762a", "PortainerAuthorizations": {"PortainerDockerHubInspect": true, "PortainerEndpointGroupList": true, "PortainerEndpointInspect": true, "PortainerEndpointList": true, "PortainerMOTD": true, "PortainerRegistryInspect": true, "PortainerRegistryList": true, "PortainerTeamList": true, "PortainerTemplateInspect": true, "PortainerTemplateList": true, "PortainerUserCreateToken": true, "PortainerUserInspect": true, "PortainerUserList": true, "PortainerUserListToken": true, "PortainerUserMemberships": true, "PortainerUserRevokeToken": true}, "Role": 1, "ThemeSettings": {"color": ""}, "TokenIssueAt": 0, "UseCache": false, "Username": "admin"}, {"EndpointAuthorizations": null, "Id": 2, "Password": "$2a$10$WpCAW8mSt6FRRp1GkynbFOGSZnHR6E5j9cETZ8HiMlw06hVlDW/Li", "PortainerAuthorizations": {"PortainerDockerHubInspect": true, "PortainerEndpointGroupList": true, "PortainerEndpointInspect": true, "PortainerEndpointList": true, "PortainerMOTD": true, "PortainerRegistryInspect": true, "PortainerRegistryList": true, "PortainerTeamList": true, "PortainerTemplateInspect": true, "PortainerTemplateList": true, "PortainerUserCreateToken": true, "PortainerUserInspect": true, "PortainerUserList": true, "PortainerUserListToken": true, "PortainerUserMemberships": true, "PortainerUserRevokeToken": true}, "Role": 1, "ThemeSettings": {"color": ""}, "TokenIssueAt": 0, "UseCache": false, "Username": "<PERSON>rabhat"}], "version": {"VERSION": "{\"SchemaVersion\":\"2.33.0-rc1\",\"MigratorCount\":1,\"Edition\":1,\"InstanceID\":\"463d5c47-0ea5-4aca-85b1-405ceefee254\"}"}, "webhooks": null}