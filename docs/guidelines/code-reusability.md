# Reusing Code across CE and BE Guidelines (Backend)

## Overview 
Portainer maintains two editions:
* Community Edition (CE) - Open source version
* Enterprise Edition (EE) - Commercial version with additional features

These are maintained as two separate folders within this repo and share a lot of similarities. To maintain code quality and reduce duplucation, we reuse code from the CE edition in the EE edition.

## How it works

Current places we reuse code:
* package/server-ce/pkg folder: Contains shared Go packages that provide core functionality used by both editions.
* package/server-ce/api folder: Backend API for CE we reuse models and shared functions etc.

```
portainer-suite/
├── package/
│   ├── server-ce/         # Community Edition
│   │   ├── pkg/          # Shared go packages
│   │   └── api/           # CE Go API
│   │
│   └── server-ee/         # Enterprise Edition
│       ├── models/        # EE-specific models
│   │   └── api/           # EE Go API
```

### Importing Shared Code into EE

The EE edition imports and extends shared code from CE using several patterns:

1. Direct Imports:
```go
   import (
       portainer "github.com/portainer/portainer/api"
   )
```
 Note: The go.mod in EE uses a replace directive:

```go
   replace github.com/portainer/portainer => ../server-ce
```


2. Interface Embedding:
```go
   type DataService interface {
       portainer.DataService  // Embed CE interface
       AdditionalMethod()     // Add EE-specific methods
   }
```
3. Type Composition:
```go
   type Endpoint struct {
       portainer.Endpoint    // Embed CE type
       AdditionalField string // Add EE-specific fields
   }
```

## Best Practices

#### Code Reuse:
* Reuse code for functionality that is identical in both editions
* Only extend shared code when adding EE-specific features
* Keep shared interfaces minimal and focused
#### Maintenance:
* Consider EE implications when modifying shared CE code
* Test changes in both CE and EE environments
#### Design:
* Maintain clear boundaries between CE and EE functionality
* Prefer reusing/extending whole packages over individual functions/models
#### Testing:
* Add tests for both CE and EE when modifying shared code

By following these practices, we maintain a clean separation between CE and EE while maximizing code reuse and minimizing duplication.
