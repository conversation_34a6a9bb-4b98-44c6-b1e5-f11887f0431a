# Standard Logging Library

All our software should use the same logging library. We’ve standardized on [zerolog](https://github.com/rs/zerolog).

# The Logging Format

We want to leverage the contextual logging approach as the base for our standard logging format. For more details, refer to the [zerolog documentation on contextual logging](https://github.com/rs/zerolog?tab=readme-ov-file#contextual-logging).

```go
// An example of contextual logging
log.Debug().
	Str("Scale", "833 cents").
	Float64("Interval", 833.09).
	Msg("<PERSON><PERSON><PERSON><PERSON> is everywhere")
```

Any other form of logging (such as `log.Print*`) must be replaced to adhere to the contextual logging format.

## The Logging Message Pattern

We should aim for the following log format:

`(TIMESTAMP) (LOG_LEVEL) (LOC) > (MESSAGE) | context=(CONTEXT) (field)=(value)`

Note that `(field)=(value)` is optional and can be repeated as many times as necessary to provide more information about specific variable values.

You can find more information about the `context` field in the section below.

## Provide Context

The context/area where a message originates must be included. While a line number can be helpful for developers, it is often more valuable for support/customers to identify the area causing the problem. This context should be defined with support/customer in mind, not just developers.

```go
// Instead of this logging statement
log.
	Error().
	Err(err).
	Msg("AdminMonitor failed to determine if Portainer is Initialized")
	
// Let's add additional context
log.
	Error().
	Err(err).
	// Add the context
	// If you're not sure about what to use as a piece of context, look within
	// the package you're currently updating and try to re-use the same context
	// definition. If it doesn't exist, create a new one but make sure it is
	// meaningful !
	Str("context", "AdminUserInitBackgroundMonitor").
	Msg("AdminMonitor failed to determine if Portainer is initialized")
```

## Logging Message Style Guide

- A logging message should always start with a capital letter and use punctuation to separate different sentences.
- Use regular capitalization across sentences.
- Messages should not end with a period.
- Newline character `\n` is not necessary.

```go
// This message is not valid
log.
	Msg("adminMonitor failed to Determine If Portainer Is Initialized.\n")
	
// This message is valid
log.
	Msg("AdminMonitor failed to determine if Portainer is initialized")
```

- Variables must use **snake case** and always be lower cased.

```go
// This is not valid
log.
	Str("variable A", varA).
	Int("anotherVariable", varB).
	Obj("andanothervariable", varC)
	
// This is valid
log.
	Str("variable_a", varA).
	Int("another_variable", varB).
	Obj("and_another_variable", varC)
```

## **Logging Levels Guide**

Here’s simple guidelines on when to use the different logging levels:

![logging-flow.png](../images/logging-flow.png)

Shamelessly sourced and adapted from https://stackoverflow.com/a/64806781

### When to use Debug ?

Debug logs should trace different steps in an operation and include various variable values useful for diagnosing issues.

### When to use Warn ?

> [!WARNING]
> Be careful with Warn log messages within loop-based operations to avoid log flooding.

Warn logs should indicate an unwanted state. For example, this could be a failure inside an operation that doesn’t prevent the operation from running or the use of an insecure configuration for a component.

### When to use Info ?

> [!WARNING]
> Be careful with Info log messages within loop-based operations to avoid log flooding.

Info logs should display useful information such as the application’s status, configuration, or the start/stop status of components.

### When to use Error ?

Error logs should indicate a problem that is fatal to the operation but not to the application.

### When to use Fatal ?

Fatal logs will force a shutdown of the application/service and should only indicate a broader system error. For web server-based applications, these are typically used at app initialization and never afterward.

### What about Trace ?

Trace logs are similar to Debug logs but without variables and are purely for diagnostics. We will not use Trace logs and will continue to use Debug logs instead.

# **Error Handling**

### Error Message Style Guide

- An error message should always start with a lowercase letter and use punctuation to separate different sentences.
- When wrapping an error (see the fmt.Errorf pattern section below), always include the original error at the end.
- Newline character `\n` should not be included.

```go
// Examples
err1 := fmt.Errorf("this is an error. It is caused by this process. Error: %w", err)
err2 := errors.New("this is an error.")
err3 := errors.New("this is an error. It is caused by this process.")
```

### The fmt.Errorf Pattern

For error handling, use the fmt.Errorf pattern as much as possible. It should always be used instead of returning a raw error and, if possible, to add more context when building your own errors.

An error should never be returned without a message that can help the user pinpoint the cause of the issue.

> [!NOTE]
> Even though we insist on never logging without a message accompanying the error, it may be acceptable in specific cases where the error message is already clear enough. Use your judgment in these situations.

Example 1: Wrapping an error

```go
// Instead of a using basic error handling
if err := functionThatReturnsAnError(); err != nil {
		return err
}

// Use the fmt.Errorf pattern to give more context and wrap the error with %w
if err := functionThatReturnsAnError(); err != nil {
		return fmt.Errorf("an error occured [give more context about why it could have happened]. Error: %w", err)
}

```

Example 2: Defining an error

```go
// When building an error, use errors.New to define a simple static error message
if somethingWentBad {
		return errors.New("an error occured in processA")
}

// However, when possible add more context to your errors when it is available
if somethingWentBad {
		return fmt.Errorf("an error occured in processA. important_variable=%s", variable)
}
```

# Some Good Reads

Here are a few useful articles and references around logging in general:

- https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
- https://google.github.io/styleguide/go/best-practices.html#error-handling