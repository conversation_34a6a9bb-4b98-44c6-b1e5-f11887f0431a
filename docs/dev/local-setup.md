# Portainer dev environment setup (for MacOS and VS Code)

## Install dependencies
1. [Install the latest version of go](https://go.dev/doc/install)

2. Install golangci-lint v2.3.1:
    ```
    # Install
    curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/HEAD/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.3.1
    echo 'export PATH=$PATH:$(go env GOPATH)/bin' >> ~/.bashrc

    # Restart bash
    bash

    # Check it's working
    golangci-lint --version
    ```

3. [Install OrbStack (to run Docker)](https://orbstack.dev/download)

4. Install Node Version Manager:
    ```
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
    ```

5. Install Node version 18.20.4
    ```
    nvm install 18.20.4
    node --version
    ```

6. Install Yarn version 1.22.22
    ```
    corepack enable
    yarn set version 1.22.22
    ```

7. Clone the Portainer Suite repo
    ```
    $ <NAME_EMAIL>:portainer/portainer-suite.git
    ```

    In order to [import private github repositories](https://go.dev/doc/faq#git_https) using `go get` we need to add an entry to `~/.gitconfig` (you may need to create this file):
    ```
    [url "ssh://**************/"]
        insteadOf = https://github.com/
    ```

## Setup VSCode
1. Setup default VSCode settings 
    ```
    cd portainer-suite
    make setup
    ```

2. Install `Go` VS Code extension

3. Install `dlv` for running the Go debugger:
    ```
    go install -v github.com/go-delve/delve/cmd/dlv@latest
    ```

## Build Portainer EE

```
make server-ee
```

## Run Portainer EE Server backend
VSCode -> Run and Debug -> Debug server-ee

API server is now available on http://localhost:9000 or https://localhost:9443

> [!NOTE]
> You might need to restart API server if it's been longer than 5 minutes since the server started and you haven't created an account via the UI yet.

## Run Portainer EE Server UI
```
cd (project root)/package/server-ee
yarn dev
```

UI is now available at http://localhost:8999/

## Extra tools

### Bolt browser

A utility for inspecting BoltDB files: https://github.com/br0xen/boltbrowser