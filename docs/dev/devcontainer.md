# Developing with dev containers

## What is this?

This toolkit enables you to develop Portainer software within a development container, providing a streamlined and isolated environment.

> [!NOTE]
>
> A development container (or “dev container”) is a container that serves as a full-featured development environment. It encapsulates all dependencies and settings needed for development, allowing for consistency and ease of setup across machines.
> 
> For more information about dev containers, see [https://containers.dev](https://containers.dev/).

## Why would I use this?

Using development containers offers several benefits over installing the full development stack directly on your machine:

- **Quick Start for New Team Members**

If you’ve recently joined the Portainer engineering team, the toolkit streamlines your setup, making it easier to start contributing immediately.

- **Environment Isolation**

If you prefer keeping your machine free from numerous dependencies, dev containers allow you to keep the toolkit and its dependencies within a container, maintaining a clean and organized environment on your host machine.

- **Simplified Dependency Management**

Managing versions for Golang, Node, Docker CLI, and other components can be cumbersome. With dev containers, all components are pre-configured and managed within the container, freeing you from manual updates or conflicts between versions.

## How can I use this?

To get started with the development container toolkit, follow these steps:

1. **Install Docker**

Docker is required to run the development container environment.

2. **Enable Development Containers in Your IDE**

Ensure your IDE supports development containers. For a list of compatible editors, refer to the [supported editors](https://containers.dev/supporting#editors) on https://containers.dev.

*Example for VS Code*: Install the [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers).

3. **Review the Documentation**

Detailed instructions on setup and usage are available in the toolkit documentation. Make sure to consult it on [GitHub](https://github.com/portainer/dev-toolkit).

For more in-depth guidance, including configuration options and advanced usage, visit the [dev-toolkit GitHub repository](https://github.com/portainer/dev-toolkit).