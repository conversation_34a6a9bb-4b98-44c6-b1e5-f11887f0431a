# How to setup your development environment

Welcome to the Portainer development team!

This guide will help you setup your development environment so you can start contributing to Portainer.

We currently support two different ways to setup your development environment:

- [Using a development container](#using-a-development-container)
- [Using a local setup](#using-a-local-setup)

## Using a development container

For more information about using development containers, please refer to the [devcontainer.md](devcontainer.md) file.

## Using a local setup

For more information about using a local setup, please refer to the [local-setup.md](local-setup.md) file.

