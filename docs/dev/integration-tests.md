# Developer Guide: Running Testcontainers Integration Tests

## Project Overview
Testcontainers is a testing library that provides lightweight, throwaway instances of common databases, message brokers, and other services in Docker containers. In the Portainer project, testcontainers is used to create isolated testing environments for integration tests, particularly for testing the Portainer server and its interactions with Docker and Kubernetes.

## Key Components:
* Portainer Server Container
* Kubernetes (K3s) Container for K8s-related tests
* Docker network for container communication
* Integration test suites in Go which are found at `/package/server-ee/test/integration`

## Prerequisites
1. Docker installed and running
2. Go development environment set up
3. Make build tools installed

#### Building Local Images
Before running the integration tests, you need to build a local image of the Portainer server:
1. Build all required components:
```bash
cd package/server-ee
make build-all
```
2. Create a local Portainer server image for testing (note: you may need to add PLATFORM=linux ARCH=amd64 depending on your environment):
```bash
make build-image TAG=local-integration-test
```
This will create an image tagged as portainerci/portainer-ee:local-integration-test that will be used by the integration tests. Note this image will contain the current portainer implementation and these steps will need to be repeated to build a new up to date image if required.

## Running Integration Tests
#### Terminal:
```bash
cd package/server-ee/test/integration/api
go test -v -timeout 15m ./... -tags=integration
```
#### VSCode Test
The tests can be run through VSCode test however the default timeout will need to be updated from 30 sec. Add ` "go.testTimeout": "300s",` to .vscode/settings.json