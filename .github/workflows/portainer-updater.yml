name: "portainer-updater"

on:
  workflow_dispatch:
  push:
    paths:
      - "apps/managed/portainer-updater/**"
    branches:
      - "develop"
      - "release/**"      
  pull_request:
    paths:
      - "apps/managed/portainer-updater/**"
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/portainer-updater

jobs:
  run-linters:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    permissions:
      checks: write
      contents: read
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: apps/managed/portainer-updater/go.mod
          cache-dependency-path: apps/managed/portainer-updater/go.sum

      - name: 'Start Timing golangci-lint'
        run: echo "GOLANGCI_LINT_START_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: 'run golangci-lint'
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.4.0
          args: --timeout=15m -c .golangci.yaml
          working-directory: './apps/managed/portainer-updater'

      - name: 'End Timing golangci-lint'
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - GOLANGCI_LINT_START_TIME))
          echo "golangci-lint duration: ${DURATION} seconds"
        shell: bash
        env:
          GOLANGCI_LINT_START_TIME: ${{ env.GOLANGCI_LINT_START_TIME }}

  run-tests:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: apps/managed/portainer-updater/go.mod
          cache-dependency-path: apps/managed/portainer-updater/go.sum

      - name: 'Run tests'
        run: make test
        working-directory: './apps/managed/portainer-updater'

      - name: 'Upload portainer-updater coverage to Codecov'
        uses: codecov/codecov-action@v5
        with:
          files: ./apps/managed/portainer-updater/coverage.out
          flags: portainer-updater
          token: ${{ secrets.CODECOV_TOKEN }}

  build-images:
    if: github.event.pull_request.draft == false
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    strategy:
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
          - { platform: linux, arch: arm, version: "" }
          - { platform: linux, arch: ppc64le, version: "" }
          - { platform: windows, arch: amd64, version: 1809 }
          - { platform: windows, arch: amd64, version: ltsc2022 }
          - { platform: windows, arch: amd64, version: ltsc2025 }
    steps:
      - name: "[preparation] checkout"
        uses: actions/checkout@v4.2.2

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: apps/managed/portainer-updater/go.mod
          cache-dependency-path: apps/managed/portainer-updater/go.sum

      - name: "[preparation] set up qemu"
        uses: docker/setup-qemu-action@v3.6.0

      - name: "[preparation] set up docker context for buildx"
        run: docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set the container image tag"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr-${{ github.event.number }} as the tag for pull requests
            # for instance, pr-123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi
          
          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: "[execution] build portainer-updater binary"
        run: make build PLATFORM=${{ matrix.config.platform }} ARCH=${{ matrix.config.arch }}
        working-directory: './apps/managed/portainer-updater'

      - name: "[cleanup] remove git repository information"
        run: |
          # Capture the git commit hash
          GIT_COMMIT=$(git log -1 --format=%h)
          echo "GIT_COMMIT=${GIT_COMMIT}" >> $GITHUB_ENV
          # Remove git repository information
          rm -rf .git
        
      - name: "[execution] build and push docker images"
        run: |
          if [ "${{ matrix.config.platform }}" == "windows" ]; then
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg OSVERSION=${{ matrix.config.version }} --build-arg GIT_COMMIT=${GIT_COMMIT} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/windows/Dockerfile .
          else
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg GIT_COMMIT=${GIT_COMMIT} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/linux/Dockerfile .
          fi
        working-directory: './apps/managed/portainer-updater'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}
          GIT_COMMIT: ${{ env.GIT_COMMIT }}

  build-manifests:
    needs: [build-images]
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    steps:
      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: '[preparation] set up docker context for buildx'
        run: docker version && docker context create builders

      - name: '[preparation] set up docker buildx'
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[execution] build and push manifests"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windows1809-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2022-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2025-amd64"
