name: "helper-reset-password"

on:
  workflow_dispatch:
  push:
    paths:
      - 'apps/helpers/helper-reset-password/**'
    branches:
      - 'develop'
      - 'release/*'
  pull_request:
    paths:
      - 'apps/helpers/helper-reset-password/**'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/helper-reset-password

jobs:
  build-images:
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
          - { platform: linux, arch: arm, version: "" }
          - { platform: linux, arch: ppc64le, version: "" }
          - { platform: windows, arch: amd64, version: 1809 }
          - { platform: windows, arch: amd64, version: ltsc2022 }
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    steps:
      - name: "[preparation] checkout the current branch"
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: "[preparation] set up golang"
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: apps/helpers/helper-reset-password/go.mod
          cache-dependency-path: apps/helpers/helper-reset-password/go.sum

      - name: "[preparation] set up qemu"
        uses: docker/setup-qemu-action@v3.6.0

      - name: "[preparation] set up docker context for buildx"
        run: docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set the container image tag"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi
          
          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: "[execution] build binary"
        run: |
          mkdir -p bin/
          if [ "${{ matrix.config.platform }}" == "windows" ]; then
            GOOS=${{ matrix.config.platform }} GOARCH=${{ matrix.config.arch }} CGO_ENABLED=0 go build -o bin/helper-reset-password.exe cmd/helper-reset-password/main.go
          else
            GOOS=${{ matrix.config.platform }} GOARCH=${{ matrix.config.arch }} CGO_ENABLED=0 go build -o bin/helper-reset-password cmd/helper-reset-password/main.go
          fi
        working-directory: './apps/helpers/helper-reset-password'

      - name: "[cleanup] remove git repository information"
        run: rm -rf .git

      - name: "[execution] build and push docker images"
        run: |
          if [ "${{ matrix.config.platform }}" == "windows" ]; then
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg OSVERSION=${{ matrix.config.version }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f Dockerfile.windows .
          else
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f Dockerfile .
          fi
        working-directory: './apps/helpers/helper-reset-password'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}

  build-manifests:
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    needs: [build-images]
    steps:
      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set up docker context for buildx"
        run: docker version && docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1  

      - name: "[execution] build and push manifests"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windows1809-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2022-amd64"
