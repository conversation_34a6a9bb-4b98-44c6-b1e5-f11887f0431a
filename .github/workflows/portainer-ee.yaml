---
name: "portainer-ee"

on:  # yamllint disable rule:truthy
  workflow_dispatch:
  push:
    paths:
      - 'package/server-ee/**'
      - 'package/server-ce/api/**'
      - 'package/server-ce/pkg/**'
      - 'package/server-ce/go.mod'
      - '.github/workflows/portainer-ee.yaml'
    branches:
      - 'develop'
      - 'release/*'
  pull_request:
    paths:
      - 'package/server-ee/**'
      - 'package/server-ce/api/**'
      - 'package/server-ce/pkg/**'
      - 'package/server-ce/go.mod'
      - '.github/workflows/portainer-ee.yaml'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/portainer-ee
  NODE_VERSION: 18.x
  GOPRIVATE: 'github.com/portainer/*'

jobs:
  run-linters:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    permissions:
      checks: write
      contents: read
    steps:
      - name: '[preparation] install octokit library'
        run: npm install @octokit/core@5.1.0 @octokit/auth-app@6.1.1 node-fetch

      - name: '[preparation] fetch github app token'
        uses: actions/github-script@v7.0.1
        id: portainer-bot
        env:
          PORTAINER_BOT_ID: ${{ secrets.PORTAINER_BOT_ID }}
          PORTAINER_BOT_KEY: ${{ secrets.PORTAINER_BOT_KEY }}
          PORTAINER_BOT_INSTALLATION_ID: ${{ secrets.PORTAINER_BOT_INSTALLATION_ID }}
        with:
          script: |
            const { Octokit } = require("@octokit/core");
            const { createAppAuth, createOAuthUserAuth } = require("@octokit/auth-app");
            const appId = process.env.PORTAINER_BOT_ID;
            const privateKey = process.env.PORTAINER_BOT_KEY;
            const installationId = process.env.PORTAINER_BOT_INSTALLATION_ID;
            const appOctokit = new Octokit({
              authStrategy: createAppAuth,
              auth: {
                appId: appId,
                privateKey: privateKey,
              },
              request: {
                fetch: fetch,
              },
            });
            const resp = await appOctokit.auth({
              type: 'installation',
              installationId,
            });
            return resp.token;
          result-encoding: string

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}
          token: ${{ steps.portainer-bot.outputs.result }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/server-ee/go.mod
          cache-dependency-path: package/server-ee/go.sum

      - name: '[preparation] configure git for private module access'
        run: |
          git config --global url."https://x-access-token:${{ steps.portainer-bot.outputs.result }}@github.com/".insteadOf "https://github.com/"

      - name: '[preparation] set up node.js'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: package/server-ee/yarn.lock

      - name: '[preparation] yarn lockfile configuration'
        run: yarn --frozen-lockfile
        working-directory: './package/server-ee'

      - name: '[fix] path fixes for eslint and tsc'
        run: |
          mkdir -p /home/<USER>/work/portainer-suite/portainer-suite/node_modules/typescript/bin
          ln -s /home/<USER>/work/portainer-suite/portainer-suite/package/server-ee/node_modules/.bin/tsc /home/<USER>/work/portainer-suite/portainer-suite/node_modules/typescript/bin/tsc
          ln -s /home/<USER>/work/portainer-suite/portainer-suite/package/server-ee/node_modules/.bin/eslint /home/<USER>/work/portainer-suite/portainer-suite/package/server-ee/eslint

      - name: 'run linters'
        uses: wearerequired/lint-action@v2
        with:
          eslint: true
          eslint_extensions: ts,tsx,js,jsx
          eslint_dir: package/server-ee
          prettier: true
          prettier_dir: package/server-ee/app
          gofmt: true
          gofmt_dir: package/server-ee/api
          check_name: ${linter}-on-${dir}

      - name: 'run typescript check'
        uses: icrawl/action-tsc@v1
        with:
          project: 'package/server-ee'

      - name: 'Start Timing golangci-lint'
        run: echo "GOLANGCI_LINT_START_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: 'run golangci-lint'
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.4.0
          args: --timeout=15m -c .golangci.yaml
          working-directory: './package/server-ee'

      - name: 'End Timing golangci-lint'
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - GOLANGCI_LINT_START_TIME))
          echo "golangci-lint duration: ${DURATION} seconds"
        shell: bash
        env:
          GOLANGCI_LINT_START_TIME: ${{ env.GOLANGCI_LINT_START_TIME }}

  run-client-tests:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up node.js'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: package/server-ee/yarn.lock

      - name: 'Yarn lockfile configuration'
        run: yarn --frozen-lockfile
        working-directory: './package/server-ee'
      
      - name: 'Clean Vitest cache'
        run: rm -rf .vitest
        working-directory: './package/server-ee'

      - name: 'Run tests'
        run: make test-client ARGS="--maxWorkers=2 --minWorkers=1"
        working-directory: './package/server-ee'

      - name: Upload frontend-ee coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          files: ./package/server-ee/coverage/coverage-final.json
          flags: frontend-ee
          token: ${{ secrets.CODECOV_TOKEN }}

  run-server-tests:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] install octokit library'
        run: npm install @octokit/core@5.1.0 @octokit/auth-app@6.1.1 node-fetch

      - name: '[preparation] fetch github app token'
        uses: actions/github-script@v7.0.1
        id: portainer-bot
        env:
          PORTAINER_BOT_ID: ${{ secrets.PORTAINER_BOT_ID }}
          PORTAINER_BOT_KEY: ${{ secrets.PORTAINER_BOT_KEY }}
          PORTAINER_BOT_INSTALLATION_ID: ${{ secrets.PORTAINER_BOT_INSTALLATION_ID }}
        with:
          script: |
            const { Octokit } = require("@octokit/core");
            const { createAppAuth, createOAuthUserAuth } = require("@octokit/auth-app");
            const appId = process.env.PORTAINER_BOT_ID;
            const privateKey = process.env.PORTAINER_BOT_KEY;
            const installationId = process.env.PORTAINER_BOT_INSTALLATION_ID;

            const appOctokit = new Octokit({
              authStrategy: createAppAuth,
              auth: {
                appId: appId,
                privateKey: privateKey,
              },
              request: {
                fetch: fetch,
              },
            });

            const resp = await appOctokit.auth({
              type: 'installation',
              installationId,
            });

            return resp.token;
          result-encoding: string

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}
          token: ${{ steps.portainer-bot.outputs.result }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/server-ee/go.mod
          cache-dependency-path: package/server-ee/go.sum

      - name: '[preparation] configure git for private module access'
        run: |
          git config --global url."https://x-access-token:${{ steps.portainer-bot.outputs.result }}@github.com/".insteadOf "https://github.com/"

      - name: Run tests
        run: make test-server
        working-directory: './package/server-ee'

      - name: Upload backend-ee coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          files: ./package/server-ee/coverage.out
          flags: backend-ee
          token: ${{ secrets.CODECOV_TOKEN }}

  # yamllint disable rule:braces
  build-images:
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
          - { platform: linux, arch: arm, version: "" }
          - { platform: linux, arch: ppc64le, version: "" }
          - { platform: windows, arch: amd64, version: 1809 }
          - { platform: windows, arch: amd64, version: ltsc2022 }
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] install octokit library'
        run: npm install @octokit/core@5.1.0 @octokit/auth-app@6.1.1 node-fetch

      - name: '[preparation] fetch github app token'
        uses: actions/github-script@v7.0.1
        id: portainer-bot
        env:
          PORTAINER_BOT_ID: ${{ secrets.PORTAINER_BOT_ID }}
          PORTAINER_BOT_KEY: ${{ secrets.PORTAINER_BOT_KEY }}
          PORTAINER_BOT_INSTALLATION_ID: ${{ secrets.PORTAINER_BOT_INSTALLATION_ID }}
        with:
          script: |
            const { Octokit } = require("@octokit/core");
            const { createAppAuth, createOAuthUserAuth } = require("@octokit/auth-app");
            const appId = process.env.PORTAINER_BOT_ID;
            const privateKey = process.env.PORTAINER_BOT_KEY;
            const installationId = process.env.PORTAINER_BOT_INSTALLATION_ID;

            const appOctokit = new Octokit({
              authStrategy: createAppAuth,
              auth: {
                appId: appId,
                privateKey: privateKey,
              },
              request: {
                fetch: fetch,
              },
            });

            const resp = await appOctokit.auth({
              type: 'installation',
              installationId,
            });

            return resp.token;
          result-encoding: string

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}
          token: ${{ steps.portainer-bot.outputs.result }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/server-ee/go.mod
          cache-dependency-path: package/server-ee/go.sum

      - name: '[preparation] set up node.js'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: package/server-ee/yarn.lock

      - name: '[preparation] configure git for private module access'
        run: |
          git config --global url."https://x-access-token:${{ steps.portainer-bot.outputs.result }}@github.com/".insteadOf "https://github.com/"

      - name: '[preparation] docker login'
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: '[preparation] set up qemu'
        uses: docker/setup-qemu-action@v3.6.0

      - name: '[preparation] set up docker context for buildx'
        run: docker context create builders

      - name: '[preparation] set up docker buildx'
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: '[preparation] set the container image tag'
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: '[execution] build linux & windows portainer binaries'
        run: |
          export YARN_VERSION=$(yarn --version)
          export WEBPACK_VERSION=$(yarn list webpack --depth=0 | grep webpack | awk -F@ '{print $2}')
          export BUILDNUMBER=${GITHUB_RUN_NUMBER}
          GIT_COMMIT_HASH_LONG=${{ github.sha }}
          export GIT_COMMIT_HASH_SHORT={GIT_COMMIT_HASH_LONG:0:7}

          NODE_ENV="testing"
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            NODE_ENV="production"
          fi

          make build-all PLATFORM=${{ matrix.config.platform }} ARCH=${{ matrix.config.arch }} ENV=${NODE_ENV}
        working-directory: './package/server-ee'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}

      - name: "[cleanup] remove git repository information"
        run: rm -rf .git

      - name: '[execution] build and push docker images'
        run: |
          if [ "${{ matrix.config.platform }}" == "windows" ]; then
            mv dist/portainer dist/portainer.exe
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg OSVERSION=${{ matrix.config.version }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/${{ matrix.config.platform }}/Dockerfile .
          else
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/${{ matrix.config.platform }}/Dockerfile .
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" -f build/${{ matrix.config.platform }}/alpine.Dockerfile .
          fi
        working-directory: './package/server-ee'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}
  # yamllint enable rule:braces

  build-manifests:
    runs-on: ubuntu-latest
    needs: [build-images, run-linters, run-client-tests, run-server-tests]
    steps:
      - name: '[preparation] docker login'
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: '[preparation] set up docker context for buildx'
        run: docker version && docker context create builders

      - name: '[preparation] set up docker buildx'
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: '[execution] build and push manifests'
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windows1809-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2022-amd64"

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le-alpine"

  playwright-platform-tests:
    needs: [build-manifests]
    if: github.event.pull_request.draft == false && vars.PLAYWRIGHT_INTEGRATION_EE_PLATFORM_TESTS == 'true'
    strategy:
      fail-fast: false
      matrix:
        environment:
          # Docker environments
          - name: standalone-socket
            runner: [playwright, docker]
            orchestrator: standalone
            userns: host
          - name: standalone-agent
            runner: [playwright, docker]
            orchestrator: standalone
            userns: host
          - name: standalone-edge-agent-standard
            runner: [playwright, docker]
            orchestrator: standalone
            userns: host
          # Swarm environments
          - name: swarm-agent
            runner: [playwright, swarm]
            orchestrator: swarm
            userns: host
          - name: swarm-edge-agent-standard
            runner: [playwright, swarm]
            orchestrator: swarm
            userns: host
          # Podman environments
          - name: podman-socket
            runner: [playwright, podman]
            orchestrator: podman
            userns: 'keep-id:uid=900,gid=900'
          - name: podman-agent
            runner: [playwright, podman]
            orchestrator: podman
            userns: 'keep-id:uid=900,gid=900'
          - name: podman-edge-agent-standard
            runner: [playwright, podman]
            orchestrator: podman
            userns: 'keep-id:uid=900,gid=900'
          # Kubernetes environments
          - name: k8s-local
            runner: [playwright, kubernetes]
            orchestrator: kubernetes
            userns: host
          - name: k8s-agent
            runner: [playwright, kubernetes]
            orchestrator: kubernetes
            userns: host
          - name: k8s-edge-agent-standard
            runner: [playwright, kubernetes]
            orchestrator: kubernetes
            userns: host
        shardIndex: [1, 2]
        shardTotal: [2]
    runs-on: ${{ matrix.environment.runner }}
    env:
      PLAYWRIGHT_TEST_TAG: ${{ vars.PLAYWRIGHT_INTEGRATION_TAG }}
      AZURE_REGISTRY_URL: ${{ secrets.AZURE_REGISTRY_URL }}
      AZURE_REGISTRY_USERNAME: ${{ secrets.AZURE_REGISTRY_USERNAME }}
      AZURE_REGISTRY_PASSWORD: ${{ secrets.AZURE_REGISTRY_PASSWORD }}
      NODE_VERSION: 20.x
      PORTAINER_PRODUCT_VERSION: "EE"
    steps:
      - name: '[preparation] determine image tags'
        id: image-tags
        run: |
          # Determine Portainer image tag
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            PORTAINER_TAG="pr-${{ github.event.number }}"
          else
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          echo "PORTAINER_IMAGE=registry-docker.portainercloud.io/portainerci/portainer-ee:${PORTAINER_TAG}" >> $GITHUB_ENV
          echo "PORTAINER_AGENT_IMAGE=registry-docker.portainercloud.io/${{ github.event.inputs.portainer_agent_image || 'portainerci/agent:develop' }}" >> $GITHUB_ENV

      - name: '[preparation] execute runner setup script'
        run: /opt/runner/setup.sh
        shell: bash

      - name: '[preparation] setup environment specific vars'
        shell: bash
        run: |
          IP_ADDRESS=$(hostname -i | awk '{print $1}')
          echo "CURRENTS_MACHINE_ID=$(hostname -s)" >> $GITHUB_ENV
          if [ "${{ matrix.environment.orchestrator }}" = "kubernetes" ]; then
            echo "PORTAINER_URL=https://${IP_ADDRESS}:30779" >> $GITHUB_ENV
          elif [ "${{ matrix.environment.orchestrator }}" = "podman" ]; then
            echo "PORTAINER_URL=https://*********:9443" >> $GITHUB_ENV
          else
            echo "PORTAINER_URL=https://${IP_ADDRESS}:9443" >> $GITHUB_ENV
          fi

          # Process tags from PR review or use default
          if [[ "${{ github.event_name }}" == "pull_request_review" && "${{ github.event.review.body }}" == "/ci"* ]]; then
            # Extract tags from the review
            TAGS=$(echo "${{ github.event.review.body }}" | sed 's/^\/ci //')
            # Convert comma-separated tags to regex pattern and add @ prefix
            GREP_PATTERN=$(echo $TAGS | tr ',' '\n' | while read tag; do
              if [[ ! -z "$tag" ]]; then
                echo -n "(?=.*@$(echo $tag | xargs))"
              fi
            done)
          else
            GREP_PATTERN="(?=.*${PLAYWRIGHT_TEST_TAG})"
          fi
          # Add orchestrator pattern
          GREP_PATTERN="${GREP_PATTERN}(?=.*@${{ matrix.environment.orchestrator }})"
          echo "PLAYWRIGHT_GREP_PATTERN=${GREP_PATTERN}" >> $GITHUB_ENV

      - name: '[preparation] determine system-tests branch'
        shell: bash
        run: |
          # Get the current branch name from the workflow
          CURRENT_BRANCH="${{ github.ref_name }}"
          
          # Check if a matching branch exists in the system-tests repository
          # We'll use the GitHub API to check for branch existence
          BRANCH_EXISTS=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: token ${{ secrets.PLAYWRIGHT_INTEGRATION_GITHUB_PAT }}" \
            "https://api.github.com/repos/portainer/system-tests/branches/${CURRENT_BRANCH}")
          
          if [ "$BRANCH_EXISTS" = "200" ]; then
            echo "Matching branch '${CURRENT_BRANCH}' found in system-tests repository"
            echo "SYSTEM_TESTS_REF=${CURRENT_BRANCH}" >> $GITHUB_ENV
          else
            echo "No matching branch '${CURRENT_BRANCH}' found in system-tests repository, using 'develop'"
            echo "SYSTEM_TESTS_REF=develop" >> $GITHUB_ENV
          fi

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          repository: portainer/system-tests
          ref: ${{ env.SYSTEM_TESTS_REF }}
          token: ${{ secrets.PLAYWRIGHT_INTEGRATION_GITHUB_PAT }}

      - name: '[preparation] set up node.js'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: '[preparation] enable corepack'
        run: corepack enable
        shell: bash

      - name: '[preparation] install dependencies'
        run: yarn --frozen-lockfile
        shell: bash

      - name: '[attempt 1] [execution] run tests in container'
        id: run-tests
        continue-on-error: true
        run: |
          docker run \
            --name playwright-container \
            --workdir /__w/portainer-suite/portainer-suite \
            --network host \
            --ipc=host \
            --user 900:900 \
            --userns ${{ matrix.environment.userns }} \
            -e HOME=/github/home \
            -e GITHUB_ACTIONS=true \
            -e CI=true \
            -e CURRENTS_PROJECT_ID \
            -e CURRENTS_RECORD_KEY \
            -e PLAYWRIGHT_TEST_TAG \
            -e AZURE_REGISTRY_URL \
            -e AZURE_REGISTRY_USERNAME \
            -e AZURE_REGISTRY_PASSWORD \
            -e NODE_VERSION \
            -e PORTAINER_IMAGE \
            -e PORTAINER_AGENT_IMAGE \
            -e PORTAINER_PRODUCT_VERSION \
            -e PORTAINER_URL \
            -e CURRENTS_CI_BUILD_ID \
            -e CURRENTS_MACHINE_ID \
            -e GITHUB_WORKFLOW \
            -e GITHUB_ACTION \
            -e GITHUB_EVENT_NAME \
            -e GITHUB_RUN_ID \
            -e GITHUB_RUN_ATTEMPT \
            -e GITHUB_REPOSITORY \
            -e COMMIT_INFO_BRANCH \
            -e COMMIT_INFO_MESSAGE \
            -e COMMIT_INFO_EMAIL \
            -e COMMIT_INFO_AUTHOR \
            -e COMMIT_INFO_SHA \
            -e COMMIT_INFO_TIMESTAMP \
            -e COMMIT_INFO_REMOTE \
            -e ${{ matrix.environment.orchestrator == 'standalone' && 'DOCKER_ENVIRONMENT' || matrix.environment.orchestrator == 'swarm' && 'SWARM_ENVIRONMENT' || matrix.environment.orchestrator == 'podman' && 'PODMAN_ENVIRONMENT' || 'K8S_ENVIRONMENT' }}=${{ matrix.environment.name }} \
            -v "${{ github.workspace }}":"/__w/portainer-suite/portainer-suite" \
            mcr.microsoft.com/playwright:${{ vars.PLAYWRIGHT_INTEGRATION_VERSION }} \
            npx playwright test --reporter=list --grep "${PLAYWRIGHT_GREP_PATTERN}" --project=chromium --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
        shell: bash

      - name: '[preparation] remove playwright container'
        if: steps.run-tests.outcome == 'failure'
        run: docker rm -f playwright-container
        shell: bash

      - name: '[preparation] teardown portainer instance'
        if: steps.run-tests.outcome == 'failure'
        run: |
          /opt/runner/cleanup.sh
        shell: bash

      - name: '[preparation] execute runner setup script'
        if: steps.run-tests.outcome == 'failure'
        run: /opt/runner/setup.sh
        shell: bash

      - name: '[attempt 2] [execution] run tests in container'
        if: steps.run-tests.outcome == 'failure'
        run: |
          docker run \
            --name playwright-container-retry \
            --workdir /__w/portainer-suite/portainer-suite \
            --network host \
            --ipc=host \
            --user 900:900 \
            --userns ${{ matrix.environment.userns }} \
            -e HOME=/github/home \
            -e GITHUB_ACTIONS=true \
            -e CI=true \
            -e CURRENTS_PROJECT_ID \
            -e CURRENTS_RECORD_KEY \
            -e PLAYWRIGHT_TEST_TAG \
            -e AZURE_REGISTRY_URL \
            -e AZURE_REGISTRY_USERNAME \
            -e AZURE_REGISTRY_PASSWORD \
            -e NODE_VERSION \
            -e PORTAINER_IMAGE \
            -e PORTAINER_AGENT_IMAGE \
            -e PORTAINER_PRODUCT_VERSION \
            -e PORTAINER_URL \
            -e CURRENTS_CI_BUILD_ID \
            -e CURRENTS_MACHINE_ID \
            -e GITHUB_WORKFLOW \
            -e GITHUB_ACTION \
            -e GITHUB_EVENT_NAME \
            -e GITHUB_RUN_ID \
            -e GITHUB_RUN_ATTEMPT \
            -e GITHUB_REPOSITORY \
            -e COMMIT_INFO_BRANCH \
            -e COMMIT_INFO_MESSAGE \
            -e COMMIT_INFO_EMAIL \
            -e COMMIT_INFO_AUTHOR \
            -e COMMIT_INFO_SHA \
            -e COMMIT_INFO_TIMESTAMP \
            -e COMMIT_INFO_REMOTE \
            -e ${{ matrix.environment.orchestrator == 'standalone' && 'DOCKER_ENVIRONMENT' || matrix.environment.orchestrator == 'swarm' && 'SWARM_ENVIRONMENT' || matrix.environment.orchestrator == 'podman' && 'PODMAN_ENVIRONMENT' || 'K8S_ENVIRONMENT' }}=${{ matrix.environment.name }} \
            -v "${{ github.workspace }}":"/__w/portainer-suite/portainer-suite" \
            mcr.microsoft.com/playwright:${{ vars.PLAYWRIGHT_INTEGRATION_VERSION }} \
            npx playwright test --reporter=list --grep "${PLAYWRIGHT_GREP_PATTERN}" --project=chromium --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
        shell: bash

      - name: '[cleanup] remove playwright container'
        if: always()
        run: docker rm -f playwright-container
        shell: bash

  playwright-edge-tests:
    needs: [build-manifests]
    if: github.event.pull_request.draft == false && vars.PLAYWRIGHT_INTEGRATION_EE_EDGE_TESTS == 'true'
    strategy:
      fail-fast: false
      matrix:
        environment:
          - name: docker
            runner: [playwright, docker]
            orchestrator: docker
            userns: host
          - name: swarm
            runner: [playwright, swarm]
            orchestrator: swarm
            userns: host
          - name: podman
            runner: [playwright, podman]
            orchestrator: podman
            userns: 'keep-id:uid=900,gid=900'
          - name: k8s
            runner: [playwright, kubernetes]
            orchestrator: kubernetes
            userns: host
        shardIndex: [1, 2]
        shardTotal: [2]
    runs-on: ${{ matrix.environment.runner }}
    env:
      PLAYWRIGHT_TEST_TAG: ${{ vars.PLAYWRIGHT_INTEGRATION_TAG }}
      AZURE_REGISTRY_URL: ${{ secrets.AZURE_REGISTRY_URL }}
      AZURE_REGISTRY_USERNAME: ${{ secrets.AZURE_REGISTRY_USERNAME }}
      AZURE_REGISTRY_PASSWORD: ${{ secrets.AZURE_REGISTRY_PASSWORD }}
      NODE_VERSION: 20.x
      PORTAINER_PRODUCT_VERSION: "EE"
    steps:
      - name: '[preparation] determine image tags'
        id: image-tags
        run: |
          # Determine Portainer image tag
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            PORTAINER_TAG="pr-${{ github.event.number }}"
          else
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          echo "PORTAINER_IMAGE=registry-docker.portainercloud.io/portainerci/portainer-ee:${PORTAINER_TAG}" >> $GITHUB_ENV
          echo "PORTAINER_AGENT_IMAGE=registry-docker.portainercloud.io/${{ github.event.inputs.portainer_agent_image || 'portainerci/agent:develop' }}" >> $GITHUB_ENV

      - name: '[preparation] execute runner setup script'
        run: /opt/runner/setup.sh
        shell: bash

      - name: '[preparation] setup environment specific vars'
        shell: bash
        run: |
          IP_ADDRESS=$(hostname -i | awk '{print $1}')
          echo "CURRENTS_MACHINE_ID=$(hostname -s)" >> $GITHUB_ENV
          echo "ORCHESTRATOR=${{ matrix.environment.orchestrator }}" >> $GITHUB_ENV
          if [ "${{ matrix.environment.orchestrator }}" = "kubernetes" ]; then
            echo "PORTAINER_URL=https://${IP_ADDRESS}:30779" >> $GITHUB_ENV
          elif [ "${{ matrix.environment.orchestrator }}" = "podman" ]; then
            echo "PORTAINER_URL=https://*********:9443" >> $GITHUB_ENV
          else
            echo "PORTAINER_URL=https://${IP_ADDRESS}:9443" >> $GITHUB_ENV
          fi

          # Process tags from PR review or use default
          if [[ "${{ github.event_name }}" == "pull_request_review" ]]; then
            if [[ "${{ github.event.review.body }}" == "/edge-tests "* ]]; then
              # Extract tags from the review when provided after /edge-tests
              TAGS=$(echo "${{ github.event.review.body }}" | sed 's/^\/edge-tests //')
              # Convert comma-separated tags to regex pattern and add @ prefix
              GREP_PATTERN=$(echo $TAGS | tr ',' '\n' | while read tag; do
                if [[ ! -z "$tag" ]]; then
                  echo -n "(?=.*@$(echo $tag | xargs))"
                fi
              done)
            elif [[ "${{ github.event.review.body }}" == "/edge-tests" ]]; then
              # Use default tag for simple /edge-tests command
              GREP_PATTERN="(?=.*${PLAYWRIGHT_TEST_TAG})"
            fi
          else
            GREP_PATTERN="(?=.*${PLAYWRIGHT_TEST_TAG})"
          fi
          # Add edge pattern
          GREP_PATTERN="${GREP_PATTERN}(?=.*@edge)"
          echo "PLAYWRIGHT_GREP_PATTERN=${GREP_PATTERN}" >> $GITHUB_ENV

      - name: '[preparation] determine system-tests branch'
        shell: bash
        run: |
          # Get the current branch name from the workflow
          CURRENT_BRANCH="${{ github.ref_name }}"
          
          # Check if a matching branch exists in the system-tests repository
          # We'll use the GitHub API to check for branch existence
          BRANCH_EXISTS=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: token ${{ secrets.PLAYWRIGHT_INTEGRATION_GITHUB_PAT }}" \
            "https://api.github.com/repos/portainer/system-tests/branches/${CURRENT_BRANCH}")
          
          if [ "$BRANCH_EXISTS" = "200" ]; then
            echo "Matching branch '${CURRENT_BRANCH}' found in system-tests repository"
            echo "SYSTEM_TESTS_REF=${CURRENT_BRANCH}" >> $GITHUB_ENV
          else
            echo "No matching branch '${CURRENT_BRANCH}' found in system-tests repository, using 'develop'"
            echo "SYSTEM_TESTS_REF=develop" >> $GITHUB_ENV
          fi

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          repository: portainer/system-tests
          ref: ${{ env.SYSTEM_TESTS_REF }}
          token: ${{ secrets.PLAYWRIGHT_INTEGRATION_GITHUB_PAT }}

      - name: '[preparation] set up node.js'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: '[preparation] enable corepack'
        run: corepack enable
        shell: bash

      - name: '[preparation] install dependencies'
        run: yarn --frozen-lockfile
        shell: bash

      - name: '[attempt 1] [execution] run tests in container'
        id: run-tests
        continue-on-error: true
        run: |
          docker run \
            --name playwright-container \
            --workdir /__w/portainer-suite/portainer-suite \
            --network host \
            --ipc=host \
            --user 900:900 \
            --userns ${{ matrix.environment.userns }} \
            -e HOME=/github/home \
            -e GITHUB_ACTIONS=true \
            -e CI=true \
            -e CURRENTS_PROJECT_ID \
            -e CURRENTS_RECORD_KEY \
            -e PLAYWRIGHT_TEST_TAG \
            -e AZURE_REGISTRY_URL \
            -e AZURE_REGISTRY_USERNAME \
            -e AZURE_REGISTRY_PASSWORD \
            -e NODE_VERSION \
            -e PORTAINER_IMAGE \
            -e PORTAINER_AGENT_IMAGE \
            -e PORTAINER_PRODUCT_VERSION \
            -e PORTAINER_URL \
            -e ORCHESTRATOR \
            -e CURRENTS_CI_BUILD_ID \
            -e CURRENTS_MACHINE_ID \
            -e GITHUB_WORKFLOW \
            -e GITHUB_ACTION \
            -e GITHUB_EVENT_NAME \
            -e GITHUB_RUN_ID \
            -e GITHUB_RUN_ATTEMPT \
            -e GITHUB_REPOSITORY \
            -e COMMIT_INFO_BRANCH \
            -e COMMIT_INFO_MESSAGE \
            -e COMMIT_INFO_EMAIL \
            -e COMMIT_INFO_AUTHOR \
            -e COMMIT_INFO_SHA \
            -e COMMIT_INFO_TIMESTAMP \
            -e COMMIT_INFO_REMOTE \
            -v "${{ github.workspace }}":"/__w/portainer-suite/portainer-suite" \
            mcr.microsoft.com/playwright:${{ vars.PLAYWRIGHT_INTEGRATION_VERSION }} \
            npx playwright test --reporter=list --grep "${PLAYWRIGHT_GREP_PATTERN}" --project=chromium --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
        shell: bash

      - name: '[preparation] remove playwright container'
        if: steps.run-tests.outcome == 'failure'
        run: docker rm -f playwright-container
        shell: bash

      - name: '[preparation] teardown portainer instance'
        if: steps.run-tests.outcome == 'failure'
        run: |
          /opt/runner/cleanup.sh
        shell: bash

      - name: '[preparation] execute runner setup script'
        if: steps.run-tests.outcome == 'failure'
        run: /opt/runner/setup.sh
        shell: bash

      - name: '[attempt 2] [execution] run tests in container'
        if: steps.run-tests.outcome == 'failure'
        run: |
          docker run \
            --name playwright-container \
            --workdir /__w/portainer-suite/portainer-suite \
            --network host \
            --ipc=host \
            --user 900:900 \
            --userns ${{ matrix.environment.userns }} \
            -e HOME=/github/home \
            -e GITHUB_ACTIONS=true \
            -e CI=true \
            -e CURRENTS_PROJECT_ID \
            -e CURRENTS_RECORD_KEY \
            -e PLAYWRIGHT_TEST_TAG \
            -e AZURE_REGISTRY_URL \
            -e AZURE_REGISTRY_USERNAME \
            -e AZURE_REGISTRY_PASSWORD \
            -e NODE_VERSION \
            -e PORTAINER_IMAGE \
            -e PORTAINER_AGENT_IMAGE \
            -e PORTAINER_PRODUCT_VERSION \
            -e PORTAINER_URL \
            -e ORCHESTRATOR \
            -e CURRENTS_CI_BUILD_ID \
            -e CURRENTS_MACHINE_ID \
            -e GITHUB_WORKFLOW \
            -e GITHUB_ACTION \
            -e GITHUB_EVENT_NAME \
            -e GITHUB_RUN_ID \
            -e GITHUB_RUN_ATTEMPT \
            -e GITHUB_REPOSITORY \
            -e COMMIT_INFO_BRANCH \
            -e COMMIT_INFO_MESSAGE \
            -e COMMIT_INFO_EMAIL \
            -e COMMIT_INFO_AUTHOR \
            -e COMMIT_INFO_SHA \
            -e COMMIT_INFO_TIMESTAMP \
            -e COMMIT_INFO_REMOTE \
            -v "${{ github.workspace }}":"/__w/portainer-suite/portainer-suite" \
            mcr.microsoft.com/playwright:${{ vars.PLAYWRIGHT_INTEGRATION_VERSION }} \
            npx playwright test --reporter=list --grep "${PLAYWRIGHT_GREP_PATTERN}" --project=chromium --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
        shell: bash

      - name: '[preparation] remove playwright container'
        if: always()
        run: docker rm -f playwright-container
        shell: bash
  api-integration-tests:
    needs: [build-manifests]
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] install octokit library'
        run: npm install @octokit/core@5.1.0 @octokit/auth-app@6.1.1 node-fetch
      - name: '[preparation] fetch github app token'
        uses: actions/github-script@v7.0.1
        id: portainer-bot
        env:
          PORTAINER_BOT_ID: ${{ secrets.PORTAINER_BOT_ID }}
          PORTAINER_BOT_KEY: ${{ secrets.PORTAINER_BOT_KEY }}
          PORTAINER_BOT_INSTALLATION_ID: ${{ secrets.PORTAINER_BOT_INSTALLATION_ID }}
        with:
          script: |
            const { Octokit } = require("@octokit/core");
            const { createAppAuth, createOAuthUserAuth } = require("@octokit/auth-app");
            const appId = process.env.PORTAINER_BOT_ID;
            const privateKey = process.env.PORTAINER_BOT_KEY;
            const installationId = process.env.PORTAINER_BOT_INSTALLATION_ID;

            const appOctokit = new Octokit({
              authStrategy: createAppAuth,
              auth: {
                appId: appId,
                privateKey: privateKey,
              },
              request: {
                fetch: fetch,
              },
            });

            const resp = await appOctokit.auth({
              type: 'installation',
              installationId,
            });

            return resp.token;
          result-encoding: string
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}
          token: ${{ steps.portainer-bot.outputs.result }}
      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/server-ee/go.mod
          cache-dependency-path: package/server-ee/go.sum
      - name: '[preparation] configure git for private module access'
        run: |
          git config --global url."https://x-access-token:${{ steps.portainer-bot.outputs.result }}@github.com/".insteadOf "https://github.com/"
      - name: '[preparation] determine image tags'
        id: image-tags
        run: |
          # Determine Portainer image tag
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            PORTAINER_TAG="pr-${{ github.event.number }}"
          else
            PORTAINER_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          echo "PORTAINER_IMAGE=portainerci/portainer-ee:${PORTAINER_TAG}" >> $GITHUB_ENV
      - name: Run integration tests
        env:
          USE_LOCAL_BUILD: "true"
          PORTAINER_IMAGE: ${{ env.PORTAINER_IMAGE }}
          TESTCONTAINERS_DOCKER_SOCKET_OVERRIDE: /var/run/docker.sock
        run: |
          cd package/server-ee/test/integration/api
          go test -v -timeout 15m ./... -tags=integration
