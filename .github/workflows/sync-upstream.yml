name: "sync-upstream"

on:
  workflow_dispatch:
  schedule:
    - cron: 00 13 * * *

jobs:
  sync:
    strategy:
      matrix:
        config:
          - repository: "portainer"
            path: "package/server-ce"
          - repository: "agent"
            path: "package/agent"
          - repository: "helper-reset-password"
            path: "apps/helpers/helper-reset-password"
          - repository: "portainer-updater"
            path: "apps/managed/portainer-updater"
          - repository: "compose-unpacker"
            path: "apps/managed/compose-unpacker"
        branch: ${{ fromJSON(vars.SYNC_BRANCH) }}
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] install octokit library'
        run: npm install @octokit/core@5.1.0 @octokit/auth-app@6.1.1 node-fetch

      - name: '[preparation] fetch github app token'
        uses: actions/github-script@v7.0.1
        id: portainer-bot
        env:
          PORTAINER_BOT_ID: ${{ secrets.PORTAINER_BOT_ID }}
          PORTAINER_BOT_KEY: ${{ secrets.PORTAINER_BOT_KEY }}
          PORTAINER_BOT_INSTALLATION_ID: ${{ secrets.PORTAINER_BOT_INSTALLATION_ID }}
        with:
          script: |
            const { Octokit } = require("@octokit/core");
            const { createAppAuth, createOAuthUserAuth } = require("@octokit/auth-app");
            const appId = process.env.PORTAINER_BOT_ID;
            const privateKey = process.env.PORTAINER_BOT_KEY;
            const installationId = process.env.PORTAINER_BOT_INSTALLATION_ID;

            const appOctokit = new Octokit({
              authStrategy: createAppAuth,
              auth: {
                appId: appId,
                privateKey: privateKey,
              },
              request: {
                fetch: fetch,
              },
            });

            const resp = await appOctokit.auth({
              type: 'installation',
              installationId,
            });

            return resp.token;
          result-encoding: string

      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
          ref: ${{ matrix.branch }}
          token: ${{ steps.portainer-bot.outputs.result }}

      - name: '[sync] push changes upstream'
        run: |
          git subtree push --prefix ${{ matrix.config.path }} https://x-access-token:${{ steps.portainer-bot.outputs.result }}@github.com/portainer/${{ matrix.config.repository }}.git ${{ matrix.branch }}
