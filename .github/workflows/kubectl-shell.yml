name: "kubectl-shell"

on:
  workflow_dispatch:
  push:
    paths:
      - 'apps/managed/kubectl-shell/**'
    branches:
      - 'develop'
      - 'release/*'
  pull_request:
    paths:
      - 'apps/managed/kubectl-shell/**'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/kubectl-shell

jobs:
  build-images:
    if: github.event.pull_request.draft == false
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    strategy:
      fail-fast: true
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
          - { platform: linux, arch: arm, version: "" }
          - { platform: linux, arch: ppc64le, version: "" }
    steps:
      - name: "[preparation] checkout"
        uses: actions/checkout@v4.2.2

      - name: "[preparation] set up qemu"
        uses: docker/setup-qemu-action@v3.2.0

      - name: "[preparation] set up docker context for buildx"
        run: docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[preparation] docker login"
        uses: docker/login-action@v3.3.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set the container image tag"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr-${{ github.event.number }} as the tag for pull requests
            # for instance, pr-123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi
          
          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: "[cleanup] remove git repository information"
        run: rm -rf .git

      - name: "[execution] build and push docker images"
        run: |
          docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" .
        working-directory: './apps/managed/kubectl-shell'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}

  build-manifests:
    runs-on: ${{ vars.PORTAINERCI_RUNNER_TYPE }}
    needs: [build-images]
    steps:
      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set up docker context for buildx"
        run: docker version && docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[execution] build and push manifests"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr-${{ github.event.number }} as the tag for pull requests
            # for instance, pr-123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t ${{ env.DOCKER_HUB_REPO }}:${CONTAINER_IMAGE_TAG} \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le"

          docker buildx imagetools inspect ${{ env.DOCKER_HUB_REPO }}:${CONTAINER_IMAGE_TAG}
