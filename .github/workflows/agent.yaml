---
name: "agent"

on:  # yamllint disable rule:truthy
  workflow_dispatch:
  push:
    paths:
      - 'package/agent/**'
      - 'package/server-ce/pkg/**'
    branches:
      - 'develop'
      - 'release/*'
  pull_request:
    paths:
      - 'package/agent/**'
      - 'package/server-ce/pkg/**'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/agent

jobs:
  run-linters:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/agent/go.mod
          cache-dependency-path: package/agent/go.sum

      - name: 'Start Timing golangci-lint'
        run: echo "GOLANGCI_LINT_START_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: 'run golangci-lint'
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.4.0
          args: --timeout=15m -c .golangci.yaml
          working-directory: './package/agent'

      - name: 'End Timing golangci-lint'
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - GOLANGCI_LINT_START_TIME))
          echo "golangci-lint duration: ${DURATION} seconds"
        shell: bash
        env:
          GOLANGCI_LINT_START_TIME: ${{ env.GOLANGCI_LINT_START_TIME }}

  run-agent-tests:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/agent/go.mod
          cache-dependency-path: package/agent/go.sum

      - name: 'Run unit tests'
        run: make test
        working-directory: './package/agent'

      - name: 'Upload coverage to Codecov'
        uses: codecov/codecov-action@v5
        with:
          files: ./package/agent/coverage.out
          flags: agent
          token: ${{ secrets.CODECOV_TOKEN }}

  # yamllint disable rule:braces
  build-images:
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
          - { platform: linux, arch: arm, version: "" }
          - { platform: linux, arch: ppc64le, version: "" }
          - { platform: windows, arch: amd64, version: 1809 }
          - { platform: windows, arch: amd64, version: ltsc2022 }
          - { platform: windows, arch: amd64, version: ltsc2025 }
    runs-on: ubuntu-latest
    steps:
      - name: '[preparation] checkout the current branch'
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: '[preparation] set up golang'
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/agent/go.mod
          cache-dependency-path: package/agent/go.sum

      - name: '[preparation] set up qemu'
        uses: docker/setup-qemu-action@v3.6.0

      - name: '[preparation] set up docker context for buildx'
        run: docker context create builders

      - name: '[preparation] set up docker buildx'
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: '[preparation] docker login'
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: '[preparation] set the container image tag'
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: '[execution] build linux & windows agent binaries'
        run: |
          mkdir -p dist/
          make all PLATFORM=${{ matrix.config.platform }} ARCH=${{ matrix.config.arch }}
        working-directory: './package/agent'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}

      - name: '[cleanup] remove git repository information'
        run: rm -rf .git

      - name: '[execution] build and push docker images'
        run: |
          if [ "${{ matrix.config.platform }}" == "windows" ]; then
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg OSVERSION=${{ matrix.config.version }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/${{ matrix.config.platform }}/Dockerfile .
          else
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/${{ matrix.config.platform }}/Dockerfile .
            docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" -f build/${{ matrix.config.platform }}/alpine.Dockerfile .
          fi
        working-directory: './package/agent'
        env:
          CONTAINER_IMAGE_TAG: ${{ env.CONTAINER_IMAGE_TAG }}
  # yamllint enable rule:braces

  build-manifests:
    runs-on: ubuntu-latest
    needs: [build-images, run-linters, run-agent-tests]
    steps:
      - name: '[preparation] docker login'
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: '[preparation] set up docker context for buildx'
        run: docker version && docker context create builders

      - name: '[preparation] set up docker buildx'
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: '[execution] build and push manifests'
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-ppc64le" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windows1809-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2022-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-windowsltsc2025-amd64"

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm-alpine"
