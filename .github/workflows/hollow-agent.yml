name: "hollow-agent"

on:
  workflow_dispatch:
  push:
    paths:
      - 'package/hollow-agent/**'
      - 'package/agent/**'
      - 'package/server-ce/**'
    branches:
      - 'develop'
      - 'release/*'
  pull_request:
    paths:
      - 'package/hollow-agent/**'
      - 'package/agent/**'
      - 'package/server-ce/**'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - 'develop'
      - 'release/*'
      - 'feat/*'
      - 'fix/*'
      - 'refactor/*'

env:
  DOCKER_HUB_REPO: portainerci/hollow-agent

jobs:
  build-images:
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        config:
          - { platform: linux, arch: amd64, version: "" }
          - { platform: linux, arch: arm64, version: "" }
    runs-on: ubuntu-latest
    steps:
      - name: "[preparation] checkout the current branch"
        uses: actions/checkout@v4.2.2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: "[preparation] set up golang"
        uses: actions/setup-go@v5.4.0
        with:
          go-version-file: package/hollow-agent/go.mod
          cache-dependency-path: package/hollow-agent/go.sum

      - name: 'Start Timing golangci-lint'
        run: echo "GOLANGCI_LINT_START_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: 'run golangci-lint'
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.4.0
          args: --timeout=15m -c .golangci.yaml
          working-directory: './package/hollow-agent'

      - name: 'End Timing golangci-lint'
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - GOLANGCI_LINT_START_TIME))
          echo "golangci-lint duration: ${DURATION} seconds"
        shell: bash
        env:
          GOLANGCI_LINT_START_TIME: ${{ env.GOLANGCI_LINT_START_TIME }}

      - name: "[preparation] set up qemu"
        uses: docker/setup-qemu-action@v3.6.0

      - name: "[preparation] set up docker context for buildx"
        run: docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set the container image tag"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi
          
          echo "CONTAINER_IMAGE_TAG=${CONTAINER_IMAGE_TAG}-${{ matrix.config.platform }}${{ matrix.config.version }}-${{ matrix.config.arch }}" >> $GITHUB_ENV

      - name: "[execution] build linux & windows agent binaries"
        run: |
          mkdir -p dist/
          make build PLATFORM=${{ matrix.config.platform }} ARCH=${{ matrix.config.arch }}
        working-directory: './package/hollow-agent'

      - name: "[cleanup] remove git repository information"
        run: |
          # Capture the git commit hash
          GIT_COMMIT=$(git log -1 --format=%h)
          echo "GIT_COMMIT=${GIT_COMMIT}" >> $GITHUB_ENV
          # Remove git repository information
          rm -rf .git

      - name: "[execution] build and push docker images"
        run: |
          docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg GIT_COMMIT=${GIT_COMMIT} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" -f build/${{ matrix.config.platform }}/Dockerfile .
          docker buildx build --output=type=registry --attest type=sbom --attest type=provenance,mode=max --platform ${{ matrix.config.platform }}/${{ matrix.config.arch }} --build-arg GIT_COMMIT=${GIT_COMMIT} -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" -f build/${{ matrix.config.platform }}/alpine.Dockerfile .
        working-directory: './package/hollow-agent'
        env:
          GIT_COMMIT: ${{ env.GIT_COMMIT }}

  build-manifests:
    runs-on: ubuntu-latest
    needs: [build-images]
    steps:
      - name: "[preparation] docker login"
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: "[preparation] set up docker context for buildx"
        run: docker version && docker context create builders

      - name: "[preparation] set up docker buildx"
        uses: docker/setup-buildx-action@v3.10.0
        with:
          endpoint: builders
          driver-opts: image=moby/buildkit:v0.21.1

      - name: "[execution] build and push manifests"
        run: |
          if [[ "${GITHUB_REF_NAME}" =~ ^release/.*$ ]]; then
            # use the release branch name as the tag for release branches
            # for instance, release/2.19 becomes 2.19
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | cut -d "/" -f 2)
          elif [ "${GITHUB_EVENT_NAME}" == "pull_request" ]; then
            # use pr${{ github.event.number }} as the tag for pull requests
            # for instance, pr123
            CONTAINER_IMAGE_TAG="pr-${{ github.event.number }}"
          else
            # replace / with - in the branch name
            # for instance, feature/1.0.0 -> feature-1.0.0
            CONTAINER_IMAGE_TAG=$(echo $GITHUB_REF_NAME | sed 's/\//-/g')
          fi

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64"

          docker buildx imagetools create -t "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-amd64-alpine" \
            "${DOCKER_HUB_REPO}:${CONTAINER_IMAGE_TAG}-linux-arm64-alpine"
