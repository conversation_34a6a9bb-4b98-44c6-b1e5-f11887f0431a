{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug server-ee",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceRoot}/package/server-ee/api/cmd/portainer",
      "cwd": "${workspaceRoot}",
      "env": {},
      "showLog": true,
      "args": [
        "--data",
        "${env:HOME}/portainer-data",
        "--assets",
        "${workspaceRoot}/package/server-ee/dist"
      ]
    },
    {
      "name": "Debug server-ce",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceRoot}/package/server-ce/api/cmd/portainer",
      "cwd": "${workspaceRoot}",
      "env": {},
      "showLog": true,
      "args": [
        "--data",
        "${env:HOME}/portainer-data",
        "--assets",
        "${workspaceRoot}/package/server-ce/dist"
      ]
    },
    {
      "name": "Debug agent",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceFolder}/package/agent/cmd/agent",
      "env": {
        "EDGE": "1",
        "EDGE_INSECURE_POLL": "1",
        "EDGE_ID": "1340d2b0-8d58-4997-9bb5-853747ae2e92",
        "EDGE_KEY": "***************************************************************************************************************************",
        "EDGE_ASYNC": "",
        "EDGE_GROUPS": "",
        "PORTAINER_TAGS": ""
      },
      "args": [
        "--assets=${workspaceRoot}/package/agent/dist",
        "--data=${env:HOME}/portainer-agent-data"
      ]
    }
  ]
}
