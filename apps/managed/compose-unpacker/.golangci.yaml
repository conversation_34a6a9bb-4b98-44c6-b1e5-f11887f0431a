version: "2"
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - depguard
    - errorlint
    - forbidigo
    - govet
    - ineffassign
    - perfsprint
    - staticcheck
    - unused
    - mirror
    - durationcheck
    - errorlint
    - govet
    - unconvert
    - usetesting
    - zerologlint
  settings:
    depguard:
      rules:
        main:
          deny:
            - pkg: encoding/json
              desc: use github.com/segmentio/encoding/json
            - pkg: golang.org/x/exp
              desc: exp is not allowed
            - pkg: github.com/portainer/libcrypto
              desc: use github.com/portainer/portainer/pkg/libcrypto
            - pkg: github.com/portainer/libhttp
              desc: use github.com/portainer/portainer/pkg/libhttp
    forbidigo:
      forbid:
        - pattern: ^tls\.Config$
          msg: Use crypto.CreateTLSConfiguration() instead
        - pattern: ^tls\.Config\.(InsecureSkipVerify|MinVersion|MaxVersion|CipherSuites|CurvePreferences)$
          msg: Do not set this field directly, use crypto.CreateTLSConfiguration() instead
      analyze-types: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
